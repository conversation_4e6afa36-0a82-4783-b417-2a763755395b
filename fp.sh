#!/bin/sh

# aktualni vetev se musi jmenovat master

# v config git-ftp musi byt nastaven user a url pro SCOPE master
##  git config git-ftp.dvaptaci.user dvaptaciwww
##  git config git-ftp.dvaptaci.url eris.topdigital.cz

# prvni init na ftp
##  git ftp init -s dvaptaci -P

branch_name=$(git rev-parse --abbrev-ref HEAD)

if [ $branch_name = "master" ] ; then
echo "Spusti se push aktualni vetve: '$branch_name' ..."
echo "server: eris.topdigital.cz"
echo "login: dvaptaciwww"
else
echo "Aktualni vetev neni 'master' ($branch_name) ... "
exit  
fi

/C/Users/<USER>/git-ftp/git-ftp push -s dvaptaci -P

# posunu tag www
git tag -d www
# git push origin :refs/tags/www
git tag www
# git push origin master --tags

exit 0