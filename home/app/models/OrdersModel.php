<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class OrdersModel extends BaseModel {
  
  protected $tableName = "orders";
  protected $fieldPrefix = "ord";
  
  public function insert($data) {
    $ordid = parent::insert($data);
    
    if ($ordid > 0) {
      //naplnim kod objednavky
      $today = dibi::fetch("SELECT MAX(ordid), DAY(NOW()) AS today_day, MONTH(NOW()) AS today_month, YEAR(NOW()) AS today_year FROM orders");
      $id = substr('0000'.$ordid, -4);
      $fields["ordcode"] = substr($today->today_year, 2, 2).substr('00'.$today->today_month, -2).$id;
      if($this->update($ordid, $fields)) {
        $this->logStatus($ordid, 0);
        return $ordid;
      }
    }
    return false;  
  }
  
  public function delete($id) {
    if (parent::delete($id)) {
      //vymazi vsechny polozky
      return dibi::query("DELETE FROM orditems WHERE oriordid=%i", $id);
    }
    return false;
  }
  
  public function logStatus($ordid, $status)  {
    //zapisu do logu
    return dibi::insert('orders_log', array(
      'orlordid'=>$ordid,
      'orlstatus'=>$status,
      'orldatec'=>new DateTime,
    ))->execute(dibi::IDENTIFIER);
  }


  /**
   * vytvoří objednávku z poptávbky
   *
   * @param int $reqid id poptávky
   */
  public function createFromRequest($reqid){
    $reqs = new RequestsModel();
    $req = $reqs->load($reqid);
    if ($req === false) {
      throw new ModelException('Poptávka nebyla nalezena.');
    }
    $arr = explode(" ", trim($req->reqname));
    if (count($arr) === 2) {
      $name = $arr[0];
      $lname = $arr[1];
    } else {
      $name = NULL;
      $lname = $req->reqname;
    }
    $vals = array(
      'ordadmid' => $req->reqadmid,
      'ordifirname' => $req->reqfirname,
      'ordilname' => $lname,
      'ordiname' => $name,
      'ordmail' => $req->reqmail,
      'ordtel' => $req->reqphone,
      'orddelid' => $req->reqdelid,
    );
    $ordid = $this->insert($vals);
    //vlozim polozky
    $pros = new ProductsModel();
    $pro = $pros->load($req->reqproid);
    $ordItems = new OrdItemsModel();

    $vals = array();
    $vals['oriordid'] = $ordid;
    $vals['oriproid'] = $pro->proid;
    $vals['oriprocode'] = $pro->procode;
    $vals['oriprocode2'] = $pro->procode2;
    $vals['oritypid'] = 0;
    $vals['oriname'] = $pro->proname;
    $vals['oriprice'] = $pro->proprice;
    $vals['orivatid'] = $pro->provatid;
    $vals['oricredit'] = $pro->procredit;
    $vals['oriqty'] = 1;
    $vals['oriprobigsize'] = $pro->probigsize;
    $vals['oriprooffer'] = $pro->prooffer;
    $ordItems->insert($vals);

    $row = dibi::fetch("SELECT delid, delname, CASE WHEN ".$pro->proprice." BETWEEN dellimitfrom AND dellimitto THEN 0 ELSE delprice END AS  delprice FROM deliverymodes WHERE delid=%i", $req->reqdelid);
    $vals = array();
    $vals['oriordid'] = $ordid;
    $vals['oritypid'] = 1;
    $vals['oriproid'] = 0;
    $vals['oriname'] = "dopravné a balné";
    $vals['oriprice'] = $row->delprice;
    $vals['orivatid'] = Null;
    $vals['oricredit'] = 0;
    $vals['oriqty'] = 1;
    $vals['oriprobigsize'] = 0;
    $vals['oriprooffer'] = 0;
    $ordItems->insert($vals);

    $this->recalcOrder($ordid);
    return $ordid;
  }

  //prepocita objednavku
  public function recalcOrder($id) {
    //pokud je sleva aktualizuju slevu
    $ordItems = new OrdItemsModel();
    //zjistim cenu objednaneho zbozi pro vypocet slevy
    $priceSum = dibi::fetchSingle("SELECT SUM(oriprice*oriqty) FROM orditems WHERE oriordid=%i AND oritypid=0", $id);
    //zjistim slevu na objednavce
    $discount = (double)dibi::fetchSingle("SELECT orddiscpercent FROM orders WHERE ordid=%i", $id);
    //zjistim ID polozky slevy
    $oriid = (int)dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3");
    
    if ($oriid > 0 && $discount == 0) {
      //vymazu slevu
      $ordItems->delete($oriid);
    } else if ($oriid == 0 && $discount > 0) {
      //zalozim polozku slevy
      $values = array(
        'oriordid' => $id,
        'oritypid' => 3,
        'oriqty' => 1,
        'oriname' => "sleva ".$discount."%",
        'oriprice' => round(-1 * $priceSum * $discount / 100, 0),
      );
      $ordItems->insert($values);
    } else if ($oriid > 0 && $discount > 0) {
      //upravim polozku
      $values = array(
        'oritypid' => 3,
        'oriname' => "sleva ".$discount."%",
        'oriprice' => round(-1 * $priceSum * $discount / 100, 0),
      );
      $ordItems->update($oriid, $values);
    }
    //zjistim cenu objednavky
    $priceSum = dibi::fetchSingle("SELECT SUM(oriprice*oriqty) FROM orditems WHERE oriordid=%i", $id);
    $vat = (int)dibi::fetchSingle("SELECT cfgvalue FROM config WHERE cfgcode='VATTYPE_0'");
    $vatLow = (int)dibi::fetchSingle("SELECT cfgvalue FROM config WHERE cfgcode='VATTYPE_1'");
    $priceSumVat = dibi::fetchSingle("SELECT SUM(oriprice*oriqty*(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    $priceSumVat = round($priceSumVat, 0);
    $vals = array(
      'ordprice'=>$priceSum,
      'ordpricevat'=>$priceSumVat,
    );
    $this->update($id, $vals);
  }
  
  /**
  * put your comment there...
  * 
  * @param integer $id
  */
  public function blAnalyse($order) {
    $orders = array();
    //$order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order->ordstatus != 7) {
      //vyberu vsechny objednavky ktere maji priznak spam a maji stejne udaje o ojednavateli
      $sql = "SELECT ordid, ordcode FROM orders WHERE ordstatus=7 AND ordid!=$order->ordid AND (
      ordilname='$order->ordilname' OR
      ".(empty($order->ordstlname) ? "" : " ordstlname='$order->ordstlname' OR ")."
      (ordistreet='$order->ordistreet' AND ordistreetno='$order->ordistreetno' AND ordicity='$order->ordicity' AND ordipostcode='$order->ordipostcode') OR
      ".(empty($order->ordststreet) ? "" : "(ordststreet='$order->ordststreet' AND ordststreetno='$order->ordststreetno' AND ordstcity='$order->ordstcity' AND ordstpostcode='$order->ordstpostcode') OR ")."
      ordmail='$order->ordmail'
      ".(empty($order->ordtel) ? "" : " OR ordtel='$order->ordtel'").")";
      $rows = dibi::fetchAll($sql);
      foreach ($rows as $row) {
        $orders[$row->ordid] = $row->ordid;
      }
    }  
    return($orders);
      
  }
  
  public function makeInvoice($id) {
    $orders = new OrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      throw new ModelException('Příslušná objednávka nenalezena.');
    } else {
      if (!empty($order->ordinvcode)) {
        throw new ModelException('Faktura je už vystavená.');
      } else {  
        //zjistim aktualni rok
        $year = (int)dibi::fetchSingle("SELECT YEAR(Now())"); 
        //zjistim maximalni cislo fa v tomto roce
        $lastInvCode = (int)dibi::fetchSingle("SELECT MAX(ordinvcode) FROM orders WHERE YEAR(Now())=$year");
        if ($lastInvCode === 0) {
          $str = $year.'000';
          $lastInvCode = (int)$str;
        }
        $nextInvCode = $lastInvCode + 1;  
        if (!$orders->update($id, array('ordinvcode'=>$nextInvCode, 'ordinvdate'=>new DateTime))) {
          throw new ModelException('Fakturu se nepodařilo vystavit.');  
        }
      }  
    }  
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumOrdStatus() {
    return array(
      0 => 'Čeká na zpracování',
      1 => 'Vyřizuje se',
      2 => 'Čeká na platbu',
      6 => 'Zaplaceno',
      3 => 'Odeslána',
      4 => 'Uzavřená',
      5 => 'Stornovaná',
      7 => 'Černá listina',
    );
  }
  
  public function getEnumOrdDelId() {
    //naplnim zpusoby dopravy
    $rows = dibi::fetchAll("SELECT delid, delname FROM deliverymodes WHERE delmasid=0 AND delstatus=0 ORDER BY delorder");
    $deliveryModeRows  = array();
    foreach ($rows  as $row) {
      $deliveryModeRows[$row->delname] = dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid=%i AND delstatus=0 ORDER BY delorder", $row->delid)->fetchPairs('delid', 'delname');
    }
    return $deliveryModeRows;
  }
  
  public function getEnumOrdDelIdSimple() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid> 0 AND delstatus=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
  }
   
}
?>