<?php
class MyAuthenticator extends NObject implements IAuthenticator {
  const NS_ADMIN = 'admin';
  const NS_USER  = 'user';

  public function authenticate(array $credentials) {
    list($username, $password, $namespace) = $credentials;
    if (empty($namespace)) throw new NAuthenticationException("Špatné volání authentizace.", self::IDENTITY_NOT_FOUND);

    switch ($namespace) {
       case self::NS_USER:
         $sql = "SELECT usrid AS id, usrmail AS mail, usrpassw AS password, usrstatus AS status FROM users WHERE usrmail=%s";
         $role = Null; //neni treba
         break;
       case self::NS_ADMIN:
         $sql = "SELECT admid AS id, admmail AS mail, admpassw AS password, admstatus AS status FROM admins WHERE admmail=%s";
         $role = 'admin'; //zatim natvrdo
         break;
    }
    $row = dibi::fetch($sql, $username);

    if (!$row) { // uživatel nenalezen?
        throw new NAuthenticationException("Uživatel nenalezen.", self::IDENTITY_NOT_FOUND);
    }
      
    if ($row->password !== md5($password)) { // hesla se neshodují?
        throw new NAuthenticationException("Špatné heslo.", self::INVALID_CREDENTIAL);
    }
    
    if ($row->status > 0) { // ucet je blokovany
      throw new NAuthenticationException("Účet je blokovaný. Kontaktujte správce.", self::NOT_APPROVED);  
    }
    
    unset($row->password);    
    
    return new NIdentity($row->id, $role); // vrátíme identitu
  }

}  
?>
