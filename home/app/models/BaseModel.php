<?php

class ModelException extends Exception { }

abstract class BaseModel extends NObject {
  //identifikatory vlastnosti datoveho pole  
  /** @var string nazev tabulky */
  protected $tableName;
  /** napojeni do EN db */
  protected $optionsEn = array();
  /** napojeni do Cs db */
  protected $optionsCs = array();
  /** @var string nazev tabulky s prefixem */
  private $table;
  

  public function __construct() {
    $this->table = $this->tableName;  
  }

  /**
  * vraci vlastnosti sloupce
  * 
  * @param string $colName nazev sloupce
  * @return array
  */
  public function getColProperties($colName) {
    return Null;
  }
  
  /**
  * vraci pro dany sloupec hodnotu prislusne property
  * 
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @return string 
  */
  public function getColProperty($colName, $colProperty) {
    return Null;
  }
  
  /**
  * nastavi proslusnemu sloupci hodnotu prislusne property
  * 
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @param string $propertyValue hodnota property
  * @return boolean
  */
  protected function setColProperty($colName, $colProperty, $propertyValue) {
    return true;  
  }
  
  public function getDataSource($sql="") {
    if ($sql == "") $sql = "SELECT * FROM $this->table";
    return dibi::dataSource($sql);
  }

  /**
  * vraci jeden zaznam 
  * 
  * @param integer $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
  * @param string $col nazev sloupce bez prefixu
  * @return dibiRow 
  */
  public function load($id, $col='id') {
    $colName = $this->fieldPrefix.$col;
    if ($col == 'id') {
      $f = 'i';
    } else {  
      $f = 's';
    }
    $result = $this->getDataSource()
      ->where($colName.'=%'.$f, $id)
      ->applyLimit(1)
      ->getResult();
    return $result->fetch();
  }
  
  public function update($id, $data,$setDateU = True) {
    if ($setDateU) $data[$this->fieldPrefix.'dateu'] = new DateTime;
    return dibi::update($this->table, $data)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function insert($data) {
    if (!isset($data[$this->fieldPrefix.'datec'])) {
      $data[$this->fieldPrefix.'datec'] = new DateTime;  
    }
    return dibi::insert($this->table, $data)
      ->execute(dibi::IDENTIFIER);
  }
  
  public function save(&$id, $data, $setDateU = True) {
    if ($id > 0) {
      return $this->update($id, $data, $setDateU);
    } else {
      $id = $this->insert($data);
      return($id > 0);
    }
  }

  public function delete($id) {
    return dibi::delete($this->table)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }
  
  public function fetchAll($sql) {
    $result = dibi::dataSource($sql)
      ->getResult();
    return $result->fetchAll();
  }
}