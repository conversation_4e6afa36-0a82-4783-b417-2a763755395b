<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class MenusModel extends BaseModel {
  
  protected $tableName = "menus";
  protected $fieldPrefix = "men";
  
  /**
  * pregeneruje cesty ukladane v katalogu
  * 
  */

  private function rebuildPaths() {
    $menus = new MenusModel();
    $result = dibi::query("SELECT * FROM menus"); 
    foreach ($result as $n => $row) {
      $pathstr = "";
      $pathidsstr = "";
      $path = array();
      $pathids = array();
      $pathlevel = 0;
      if ($row->menmasid > 0) {
        $lastmasid = $row->menmasid;
        do {
          $pathlevel ++;  
          $catalog = $menus->load($lastmasid);
          $lastmasid = $catalog->menmasid;
          $path[$catalog->menid] = $catalog->menname;
          $pathids[$catalog->menid] = $catalog->menid;
        } while ($lastmasid > 0);
        $path = array_reverse($path, True);
        $pathids = array_reverse($pathids, True);
        $pathstr = "|".implode("|", $path);
        $pathidsstr = "|".implode("|", $pathids);      
      }
      $pathlevel ++;
      $pathstr .= "|$row->menname|";
      $pathidsstr .= "|$row->menid|";
      
      //updatnu prislusny catalog pokud se neco zmenilo
      if ($row->menlevel != $pathlevel || $row->menpath != $pathstr || $row->menpathids != $pathidsstr) {
        $values = array();
        $values["menlevel"] = $pathlevel;
        $values["menpath"] = $pathstr;
        $values["menpathids"] = $pathidsstr;
        $menus->update($row->menid, $values, false);
      }
    } 
  }
  
  public function insert($data) {
    $ret = parent::insert($data);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret); 
  }
  
  public function update($id, $data, $setDateU = Null) {
    $ret = parent::update($id, $data, $setDateU);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret); 
  }
  
  public function delete($id) {
    $ret = parent::delete($id);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik catstatus
  * @return array
  */
  public function getEnumMenStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  } 
  
  /**
  * Vraci strom katalogu (vhodne pro combo box)
  * CACHED
  * @return array
  */
  public function getEnumMenuCombo($emptytext="") {
    $cache = NEnvironment::getCache('enum');
    if (!isset($cache['MenuCombo'])) {
      unset($cache['MenuCombo']);
      $arr = array();
      $arr[0] = "Kořenová úroveň";
      $this->addEnumMenuComboLevel(0, $arr);
      $cache['MenuCombo'] = $arr;
    }
    if ($emptytext != "") {
      return array_merge(array('' => $emptytext), $cache['MenuCombo']);
    } else {
      return $cache['MenuCombo'];  
    }
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuComboLevel($menmasid, &$arr) {
    $res = dibi::query("SELECT * FROM menus WHERE menmasid=$menmasid");
    
    while ($row = $res->fetch()) {
      //zjistim v jake urovni zanoreni jsem
      $lev = $row->menlevel + 1; 
      $arr[$row->menid] = str_repeat('-', $lev * 2).' '.$row->menname;
      $this->addEnumMenuComboLevel($row->menid, $arr);
    }  
  }
  
  /**
  * Vraci strom katalogu
  * CACHED
  * @return array
  */
  public function getEnumMenuTree() {
    $cache = NEnvironment::getCache('enum');
    if (!isset($cache['MenuTree'])) {
      unset($cache['MenuTree']);
      $arr = array();
      $arr = $this->addEnumMenuTreeLevel(0);
      $cache['MenuTree'] = $arr;
    }  
    return $cache['MenuTree'];  
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuTreeLevel($menmasid) {
    $items = dibi::query("SELECT * FROM menus WHERE menmasid=$menmasid ORDER BY menorder")
      ->fetchAssoc('menid');
    
    $arr = array();
    foreach ($items as $key => $row) {
      $arr[$key]["data"] = $row;
      $arr[$key]["subitems"] = $this->addEnumMenuTreeLevel($key);
    }
    return $arr;    
  }
  
    /**
  * ciselnik typ stranky
  * @return array
  */
  public function getEnumMenPagId() {
    $items = dibi::query("SELECT * FROM pages ORDER BY pagtypid")
      ->fetchPairs('pagid', 'pagname');    
    return $items;  
  }  
}
?>