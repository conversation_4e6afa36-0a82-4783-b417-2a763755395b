<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class DeliveryModesModel extends BaseModel {
  
  protected $tableName = "deliverymodes";
  protected $fieldPrefix = "del";
  
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumDelStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  public function getEnumPayTypes() {
    return array(
      'paybefore' => 'Platba předem',
      'paypal' => 'PayPal',
      'moneybookers' => 'Moneybookers',
    );
  }
    
  /**
  * ciselnik zpusoby dodani
  * @return array
  */
  public function getEnumDelModes() {
    $items = dibi::query("SELECT * FROM deliverymodes ORDER BY delorder")
      ->fetchPairs('delid', 'delname');
    return($items);  
  }
  
  public function getEnumDelModesByPrice($price) {
    //naplnim zpusoby dopravy podle ceny
    $items = dibi::query("SELECT * FROM deliverymodes WHERE delmasid=0 AND delstatus=0 ORDER BY delorder")
      ->fetchAssoc('delid');
    
    foreach ($items as $key => $value) {
      $items[$key]["subItems"] = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delmasid=$key AND $price BETWEEN dellimitfrom AND dellimitto AND delstatus=0 ORDER BY delorder");
    }
    return $items;
  }      
}
?>