<?php
  
class RequestsModel extends BaseModel {
  
  protected $tableName = "requests";
  protected $fieldPrefix = "req";

  public function logStatus($reqid, $status)  {
    //zapisu do logu
    return dibi::insert('requests_log', array(
      'relreqid'=>$reqid,
      'relstatus'=>$status,
      'reldatec'=>new DateTime,
    ))->execute(dibi::IDENTIFIER);
  }

  public function update($id, $data, $setDateU = TRUE) {
    $lastStatus = NULL;
    if (isset($data["reqstatus"])) {
      $lastStatus = dibi::fetchSingle("SELECT reqstatus FROM requests WHERE reqid=%i", $id);
    }
    $ret = parent::update($id, $data, $setDateU);
    if ($ret && $lastStatus !== NULL && (int)$data["reqstatus"] !== $lastStatus) {
      $this->logStatus($id, $data["reqstatus"]);
    }
    return $ret;
  }

  public function insert($data) {
    $id =  parent::insert($data);
    if ($id > 0) {
      $this->logStatus($id, 0);
    }
    return $id;
  }

  /**
  * ciselnik reqstatus
  * @return array
  */
  public function getEnumReqStatus() {
    return array(
      0 => 'Čeká na zpracování',
      1 => 'Vyřizuje se',
      2 => 'Výhra / Objednávka',
      3 => 'Uzavřená',
      4 => 'Stornovaná',
    );
  }

}
