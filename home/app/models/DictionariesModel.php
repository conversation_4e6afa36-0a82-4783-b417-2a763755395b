<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class DictionariesModel extends BaseModel {
  
  protected $tableName = "dictionaries";
  protected $fieldPrefix = "dic";
     
  static function getDictionary($lng) {
    $cache = NEnvironment::getCache('dictionaries');
    if (!isset($cache['dic'])) {
      unset($cache['dic']);
      $result = dibi::query("SELECT dicfrom, dicto_$lng FROM dictionaries")->fetchPairs('dicfrom', 'dicto_'.$lng);
      $cache->save('dic', $result, array(NCache::TAGS => array('dictionaries')));
    }
    return $cache['dic'];
  }
  
  public function cacheClean() {
    $cache = NEnvironment::getCache($this->tableName);
    $cache->clean(array(NCache::TAGS => array($this->tableName)));
  }
}
?>
