<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class UsersModel extends BaseModel {
  
  protected $tableName = "users";
  protected $fieldPrefix = "usr";
  
  public function checkDuplicityEMail($usrid, $value) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->tableName WHERE usrmail='$value'".($iusrd>0 ? " AND usrid!=$usrid" : ""));
    if ($cnt > 0) throw New Exception("Tento email již existuje.");
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumUsrStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  public function getEnumUsrPrcCat() {
    return array(
      'a' => 'A',
      'b' => 'B',
      'c' => 'C',
      'd' => 'D',
    );
  } 
}
?>