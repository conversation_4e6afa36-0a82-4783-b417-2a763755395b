<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class ProductsModel extends BaseModel {
  
  protected $tableName = "products";
  protected $fieldPrefix = "pro";
  private $prccat = 'a';
   
  public function update($id, $data,$setDateU = True) {
    if (!empty($data["prokey"])) $data["prokey"] = NStrings::webalize($data["prokey"]);
    return(parent::update($id, $data, $setDateU));
  }
  
  public function getDataSource($sql="") {
    if ($sql == "") $sql = "SELECT *, IF(proprice".$this->prccat.">0,proprice".$this->prccat.",propricea) AS proprice FROM $this->tableName";
    return dibi::dataSource($sql);
  }
  
  /** 
  * nastavi cenovou kategorii pro preddefinovane SQL
  * 
  */
  public function setPrcCat($val) {
    if ($val == "") $val = "a";
    $this->prccat = $val;
  }
  
  public function fetchAllList($where) {
    return parent::fetchAll($sql);
  }
  
  /**
  * prepocita statistiku prodejnosti
  * 
  */
  public function recalcSaleStat() {
    dibi::query("DELETE FROM products_salestat WHERE prsproid > 0");
    dibi::query("INSERT INTO products_salestat SELECT oriproid, SUM(oriqty), NOW(), NULL FROM orditems INNER JOIN orders ON (oriordid=ordid) WHERE oritypid=0 AND ordstatus IN (3,4) GROUP BY oriproid");
  }
  
  /**
  * aktualizuje zarazeni do katalogu
  * 
  * @param integer $proId
  * @param array $catPlaces
  */
  public function updateCatPlace($proId, $catIds) {
    $idlst = "";
    $catplace = new CatPlacesModel();
    foreach ($catIds as $key => $value) {
      $idlst .= $value.",";
      //zjistim jestli zaznam existuje
      $cnt = (integer)dibi::fetchSingle("SELECT count(*) FROM catplaces WHERE capcatid=$value AND capproid=$proId");
      //zaznam neexistuje
      $vals = array();
      if ($cnt == 0) {
        $vals = array('capcatid' => $value, 'capproid' => $proId);
        $catplace->insert($vals);
      }
    }
    $idlst = trim($idlst, ',');
    //vsechny ostatni zaznamy vymazu
    dibi::query("DELETE FROM catplaces WHERE capproid=$proId".($idlst != "" ? " AND capcatid NOT IN ($idlst)" : ""));
  }
  
  /**
  * kontrola duplicity kodu zbozi
  * 
  * @param integer $id - id polozky pokud se jedna o editaci
  * @param string $value  - kod zbozi
  */
  private function checkDuplicityProCode($id, $value) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->tableName WHERE procode='$value'".($id>0 ? " AND proid!=$id" : ""));
    if ($cnt > 0) throw New ModelException("Tento kód zboží již existuje.");
  }
  
  public function save(&$id, $data, $setDateU = True) {
    if ($data["procode"] != "") $this->checkDuplicityProCode($id, $data["procode"]);
    return parent::save($id, $data, $setDateU);
  }
  
  public function delete($id) {
    $ret = parent::delete($id);
    //vymazu prilohy
    dibi::query("DELETE FROM attachments WHERE ataproid=%i", $id);
    //vymazu zarazeni do catalogu
    dibi::query("DELETE FROM catplaces WHERE capproid=%i", $id);
    //vymazu products_salestat
    dibi::query("DELETE FROM products_salestat WHERE prsproid=%i", $id);
    //vymazu proparams
    dibi::query("DELETE FROM proparams WHERE prpproid=%i", $id);                                 
    return ($ret);
  }
  
  /********************* preddefinovane SQL *********************/
  
  /**
  * vraci SQL SELECT
  * seznam zbozi
  *   
  * @param string $where
  * @return string
  */
  public function getSqlList($where, $orderBy="proorder") {
    if ($where != "") $where = "WHERE $where";
    if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    return("SELECT proid, prokey, prokeymaster, procode, proname, propicname, pronames, prodescs, proaccess, propricecom, IF(proprice".$this->prccat.">0,proprice".$this->prccat.",propricea) AS proprice, provatid, proaudio proorder 
FROM products
$where $orderBy");
  }
  
  /**
  * vraci SQL SELECT
  * seznam zbozi v katalogu
  *   
  * @param string $catid - id urovne katalogu  
  * @param string $where  
  * @return string
  */
  public function getSqlCatalogList($catid, $where = "", $orderBy="proorder") {
    if ($where != "") $where = "AND ($where)";
    if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    return("SELECT proid, prokey, prokeymaster, procode, proname, pronames, prodescs, propricecom, propicname, IF(proprice".$this->prccat.">0,proprice".$this->prccat.",propricea) AS proprice, provatid, proaudio, proorder 
FROM products 
INNER JOIN catplaces ON (capproid=proid)
WHERE capcatid=$catid
$where 
GROUP BY proid
$orderBy");
  }
  
  public function genProKeyMaster($proid=0) {
    $rows = dibi::fetchAll("
      SELECT * FROM products 
      INNER JOIN catplaces ON (capproid=proid)
      INNER JOIN catalogs ON (capcatid=catid)
      ".($proid>0 ? " WHERE proid=$proid " : "")."
      GROUP BY proid
    ");
    
    foreach ($rows as $key => $row) {
      $arr = explode('|', trim($row->catpathids, '|'));
      $catmasid = (int)$arr[0];
      $cat = dibi::fetch("SELECT catid, catkey, catname FROM catalogs WHERE catid=%i", $catmasid);
      $prokeymaster = (empty($cat->catkey) ? NStrings::webalize($cat->catname) : $cat->catkey) ;
      dibi::query("UPDATE products SET prokeymaster='$prokeymaster', promasid=".$cat->catid." WHERE proid=".$row->proid);
    }
  }
  
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik prostatus
  * @return array
  */
  public function getEnumProStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  /**
  * ciselnik vyrobcu
  * @return array
  */
  public function getEnumProManId() {
    return dibi::query("SELECT manid, manname FROM manufacturers")
      ->fetchPairs('manid', 'manname');
  }
  
  /**
  * ciselnik typu zbozi
  * @return array
  */
  public function getEnumProTypId() {
    return dibi::query("SELECT enuid, enuname FROM enumcats WHERE enutypid=2")
      ->fetchPairs('enuid', 'enuname');
  }  
  
  /**
  * ciselnik sazby DPH zbozi
  * @return array
  */
  public function getEnumProVatId() {
    return array(
      0 => 'Základní',
      1 => 'Snížená',
    );
  } 
}
?>
