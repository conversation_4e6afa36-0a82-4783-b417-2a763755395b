<?php
  
class AdminsModel extends BaseModel {
  
  protected $tableName = "admins";
  protected $fieldPrefix = "adm";
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumAdmStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  /**
  * ciselnik admrole
  * @return array
  */
  public function getEnumAdmRole() {
    return array(
      'superadmin' => 'Superadmin',
      'editor' => 'Editor',
    );
  }
  
  public function getEnumAdmins($onlyActive=TRUE) {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT admid, admname FROM admins " . ($onlyActive ? " WHERE admstatus=0" : "") . " ORDER BY admname")->fetchPairs('admid', 'admname');
  }
}