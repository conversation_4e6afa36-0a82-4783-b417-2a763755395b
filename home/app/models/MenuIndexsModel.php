<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class MenuIndexsModel extends BaseModel {
  
  protected $tableName = "menuindexs";
  protected $fieldPrefix = "mei";
  
  /**
  * pregeneruje cesty ukladane v katalogu
  * 
  */

  private function rebuildPaths() {
    $menuindexs = new MenuIndexsModel();
    $result = dibi::query("SELECT * FROM menuindexs"); 
    foreach ($result as $n => $row) {
      $pathstr = "";
      $pathidsstr = "";
      $path = array();
      $pathids = array();
      $pathlevel = 0;
      if ($row->meimasid > 0) {
        $lastmasid = $row->meimasid;
        do {
          $pathlevel ++;  
          $catalog = $menuindexs->load($lastmasid);
          $lastmasid = $catalog->meimasid;
          $path[$catalog->meiid] = $catalog->meiname;
          $pathids[$catalog->meiid] = $catalog->meiid;
        } while ($lastmasid > 0);
        $path = array_reverse($path, True);
        $pathids = array_reverse($pathids, True);
        $pathstr = "|".implode("|", $path);
        $pathidsstr = "|".implode("|", $pathids);      
      }
      $pathlevel ++;
      $pathstr .= "|$row->meiname|";
      $pathidsstr .= "|$row->meiid|";
      
      //updatnu prislusny catalog pokud se neco zmenilo
      if ($row->meilevel != $pathlevel || $row->meipath != $pathstr || $row->meipathids != $pathidsstr) {
        $values = array();
        $values["meilevel"] = $pathlevel;
        $values["meipath"] = $pathstr;
        $values["meipathids"] = $pathidsstr;
        $menuindexs->update($row->meiid, $values, false);
      }
    } 
  }
  
  public function insert($data) {
    $ret = parent::insert($data);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret); 
  }
  
  public function update($id, $data, $setDateU = Null) {
    $ret = parent::update($id, $data, $setDateU);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret); 
  }
  
  public function delete($id) {
    $ret = parent::delete($id);
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik catstatus
  * @return array
  */
  public function getEnumMenStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  } 
  
  /**
  * Vraci strom katalogu (vhodne pro combo box)
  * CACHED
  * @return array
  */
  public function getEnumMenuIndexCombo($emptytext="") {
    $cache = NEnvironment::getCache('enum');
    if (!isset($cache['MenuIndexCombo'])) {
      unset($cache['MenuIndexCombo']);
      $arr = array();
      $arr[0] = "Kořenová úroveň";
      $this->addEnumMenuIndexComboLevel(0, $arr);
      $cache['MenuIndexCombo'] = $arr;
    }
    if ($emptytext != "") {
      return array_merge(array('' => $emptytext), $cache['MenuIndexCombo']);
    } else {
      return $cache['MenuIndexCombo'];  
    }
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuIndexComboLevel($meimasid, &$arr) {
    $res = dibi::query("SELECT * FROM menuindexs WHERE meimasid=$meimasid");
    
    while ($row = $res->fetch()) {
      //zjistim v jake urovni zanoreni jsem
      $lev = $row->menlevel + 1; 
      $arr[$row->menid] = str_repeat('-', $lev * 2).' '.$row->meiname;
      $this->addEnumMenuIndexComboLevel($row->meiid, $arr);
    }  
  }
  
  /**
  * Vraci strom katalogu
  * CACHED
  * @return array
  */
  public function getEnumMenuIndexTree() {
    $cache = NEnvironment::getCache('enum');
    if (!isset($cache['MenuIndexTree'])) {
      unset($cache['MenuIndexTree']);
      $arr = array();
      $arr = $this->addEnumMenuIndexTreeLevel(0);
      $cache['MenuIndexTree'] = $arr;
    }  
    return $cache['MenuIndexTree'];  
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $menmasid
  * @param array $arr
  */
  private function addEnumMenuIndexTreeLevel($meimasid) {
    $items = dibi::query("SELECT * FROM menuindexs WHERE meimasid=$meimasid ORDER BY IF(meibig=1,0,1) AND meiorder")
      ->fetchAssoc('meiid');
    
    $arr = array();
    foreach ($items as $key => $row) {
      $arr[$key]["data"] = $row;
      $arr[$key]["subitems"] = $this->addEnumMenuIndexTreeLevel($key);
    }
    return $arr;    
  }
  
    /**
  * ciselnik typ stranky
  * @return array
  */
  public function getEnumMeiPagId() {
    $items = dibi::query("SELECT * FROM pages ORDER BY pagtypid")
      ->fetchPairs('pagid', 'pagname');    
    return $items;  
  }  
}
?>