<?php
  
class EnumcatsModel extends BaseModel {
  
  protected $tableName = "enumcats";
  protected $fieldPrefix = "enu";
  
  public function getEnumEnuTypId() {
    return array(
      1 => 'Cílové země',
    );
  }
  
   public function getEnumEnuStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }
  
  public function getEnumDelRegions() {
    return array(
      1 => 'Region 1',
      2 => 'Region 2',
      3 => 'Region 3',
    );
  }
  
  public function getEnumCountries($onlyActive=True) {
    return dibi::fetchPairs("SELECT enuid AS couid, enuname AS couname FROM enumcats WHERE enutypid=1 ".($onlyActive ? ' AND enustatus=0' : '')." ORDER BY enuname");
  }
  
  
  
}
?>