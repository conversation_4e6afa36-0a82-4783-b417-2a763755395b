<?php

require_once dirname(__FILE__).'/BaseModel.php';
  
class CatalogsModel extends BaseModel {
  
  protected $tableName = "catalogs";
  protected $fieldPrefix = "cat";
  
  public function __construct() {
    parent::__construct();
    //upresneni formatu datovych poli, pokud je treba
    
  }
  
  /**
  * pregeneruje cesty ukladane v katalogu
  * 
  */

  public function rebuildPaths() {
    $catalogs = new CatalogsModel();
    $rows = dibi::query("SELECT catid, catmasid, catkeymaster, catname, catlevel,catpath,catpathids FROM catalogs")->fetchAssoc('catid'); 
    $masters = dibi::query("SELECT catid, catname, catkey FROM catalogs WHERE catmasid=0")->fetchAssoc('catid'); 
    $cnt1 = 0;
    $cnt2 = 0;
    foreach ($rows as $row) {
      $pathstr = "";
      $pathidsstr = "";
      $path = array();
      $pathids = array();
      $pathlevel = 0;
      $catkeymaster = "";
      if ($row->catmasid > 0) {
        $a = explode('|', trim($row->catpathids, '|'));
        $catrootid = (int)$a[0];
        if(isset($masters[$catrootid])) {
          $catkeymaster = (empty($masters[$catrootid]->catkey) ? NStrings::webalize($masters[$catrootid]->catname) : $masters[$catrootid]->catkey) ;
        }  
        $cnt1++;
        $lastmasid = $row->catmasid;
        do {
          $pathlevel ++;  
          $catalog = $rows[$lastmasid]; //dibi::fetch("SELECT catid, catmasid, catname FROM catalogs WHERE catid=%i", $lastmasid);
          $lastmasid = $catalog->catmasid;
          $path[$catalog->catid] = $catalog->catname;
          $pathids[$catalog->catid] = $catalog->catid;
          $cnt2++;
        } while ($lastmasid > 0);
        $path = array_reverse($path, True);
        $pathids = array_reverse($pathids, True);
        $pathstr = implode("|", $path);
        $pathidsstr = "|".implode("|", $pathids);      
      }
      $pathlevel ++;
      $pathstr .= ($pathstr != "" ? "|" : "")."$row->catname";
      $pathidsstr .= "|$row->catid|";
      
      //updatnu prislusny catalog pokud se neco zmenilo
      if ($row->catlevel != $pathlevel || $row->catpath != $pathstr || $row->catpathids != $pathidsstr  || $row->catkeymaster != $catkeymaster) {
        $values = array();
        $values["catlevel"] = $pathlevel;
        $values["catpath"] = $pathstr;
        $values["catpathids"] = $pathidsstr;
        $values["catkeymaster"] = $catkeymaster;
        $catalogs->update($row->catid, $values, false, false);
      }
    } 
  }
  
  public function insert($data, $rebuildPaths=true) {
    $catid = parent::insert($data);
    //vymazu cache
    $this->cacheClean();
    //pregeneruju cesty 
    if ($catid > 0 && $rebuildPaths) $this->rebuildPaths();
    return ($catid); 
  }
  
  public function update($id, $data, $setDateU = Null, $rebuildPaths=true) {
    if (!empty($data["catkey"])) $data["catkey"] = NStrings::webalize($data["catkey"]);
    $ret = parent::update($id, $data, $setDateU);
    //pregeneruju cesty 
    //vymazu cache
    $this->cacheClean();
    if ($rebuildPaths) $this->rebuildPaths();
    return ($ret); 
  }
  
  private function cacheClean() {
    $cache = NEnvironment::getCache($this->tableName);
    $cache->clean(array(NCache::TAGS => array($this->tableName)));
  }
  
  public function delete($id) {
    $ret = parent::delete($id);
    //vymazu prislusne zarazeni v katalogu
    dibi::query("DELETE FROM catplaces WHERE capcatid=%i", $id);
    
    //vymazu cache
    $this->cacheClean();
    
    //pregeneruju cesty 
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }
  
  /********************* ciselniky *********************/
  
  /**
  * ciselnik catstatus
  * @return array
  */
  public function getEnumCatStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  } 
  
  /**
  * Vraci strom katalogu (vhodne pro combo box)
  * CACHED
  * @return array
  */
  public function getEnumCatalogCombo($masid=0, $onlyActive=false) {
    $cache = NEnvironment::getCache($this->tableName);
    $cacheName = 'CatalogCombo'.(int)$onlyActive;
    if (!isset($cache[$cacheName])) {
      unset($cache[$cacheName]);
      $arr = array();
      $arr[0] = "Kořenová úroveň";
      $this->addEnumCatalogComboLevel($masid, $arr, $onlyActive);
      $cache->save($cacheName, $arr, array(NCache::TAGS => array($this->tableName)));
    }
    return $cache[$cacheName];  
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $catmasid
  * @param array $arr
  */
  private function addEnumCatalogComboLevel($catmasid, &$arr, $onlyActive=false) {
    $res = dibi::query("SELECT * FROM catalogs WHERE catmasid=$catmasid ".($onlyActive ? ' AND catstatus=0' : '')." ORDER BY catorder,catname");
    
    while ($row = $res->fetch()) {
      //zjistim v jake urovni zanoreni jsem
      $lev = $row->catlevel + 1; 
      $arr[$row->catid] = str_repeat('-', $lev * 2).' '.$row->catname;
      $this->addEnumCatalogComboLevel($row->catid, $arr);
    }  
  }
  
  public function addEnumCatalogRootLevel() {
    $ret = dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=0 ORDER BY catname")->fetchPairs('catid', 'catname');  
    return ($ret);
  }
  
  /**
  * Vraci strom katalogu
  * CACHED
  * @return array
  */
  public function getEnumCatalogTree($catmasid=0, $onlyActive=false) {
    $cache = NEnvironment::getCache($this->tableName);
    if (!isset($cache['CatalogMenu_'.(int)($onlyActive).'_'.$catmasid])) {
      unset($cache['CatalogMenu_'.(int)($onlyActive).'_'.$catmasid]);
      $arr = $this->addEnumCatalogTreeLevel($catmasid, $onlyActive);
      $cache->save('CatalogMenu_'.(int)($onlyActive).'_'.$catmasid, $arr, array(NCache::TAGS => array($this->tableName)));
    }
    return $cache['CatalogMenu_'.(int)($onlyActive).'_'.$catmasid]; 
  }
  
  /**
  * vraci jednu vetev katalogu
  * 
  * @param integer $catmasid
  * @param array $arr
  */
  private function addEnumCatalogTreeLevel($catmasid, $onlyActive=false) {
    $items = dibi::query("SELECT * FROM catalogs WHERE catmasid=$catmasid ".($onlyActive ? ' AND catstatus=0' : '')." ORDER BY catorder")
      ->fetchAssoc('catid');
    
    $arr = array();
    foreach ($items as $key => $row) {
      $arr[$key]["data"] = $row;
      $arr[$key]["subitems"] = $this->addEnumCatalogTreeLevel($key, $onlyActive);
    }
    return $arr;    
  }
  
  
  /**
  * vraci vypis stromu katalogu pro menu, jen prvni dve zanoreni
  * CACHED
  * @return array
  */
  public function getEnumCatalogMenu() {
    $cache = NEnvironment::getCache($this->tableName);
    if (!isset($cache['CatalogMenu'])) {
      unset($cache['CatalogMenu']);
      $sql = "SELECT cat.catid as masid, cat.catkey as maskey, cat.catname AS masname, 
cat_l12.catid AS subid, cat_l12.catkey AS subkey, cat_l12.catname AS subname 
FROM catalogs AS cat
LEFT JOIN catalogs AS cat_l12 ON (cat.catid=cat_l12.catmasid)
WHERE cat.catmasid=0 AND cat.catstatus=0 AND COALESCE(cat_l12.catstatus, 0)=0 
ORDER BY cat.catorder, cat_l12.catorder";
      $result = dibi::query($sql);
      $cache->save('CatalogMenu', $result->fetchAssoc('masid,=,subid'), array(NCache::TAGS => array($this->tableName)));
    }
    return $cache['CatalogMenu'];
  }
  
  public function getEnumRootCatId() {
    return dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=0 AND catid!=50")
      ->fetchPairs('catid', 'catname');
  }

  public function getEnumUseCatId() {
    return dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=50")
      ->fetchPairs('catid', 'catname');
  }
     
}
?>