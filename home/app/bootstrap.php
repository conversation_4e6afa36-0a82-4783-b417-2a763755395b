<?php

define('IS_PRODUCTION', $_SERVER["SERVER_NAME"] != '127.0.0.1' && $_SERVER["SERVER_NAME"] != 'localhost' && $_SERVER["SERVER_NAME"] != 'web.dvaptaci.orb.local' && strpos($_SERVER["SERVER_NAME"], 'localhost') === false && strpos($_SERVER["SERVER_NAME"], '.local') === false);

// pokud používáte verzi pro PHP 5.3, odkomentujte následující <PERSON>dek:
// use Nette\Debug, Nette\NEnvironment, Nette\Loaders\RobotLoader, Nette\Application\SimpleRouter;
//setlocale(LC_ALL, 'cs_CZ.utf-8');

//Load Nette Framework
require LIBS_DIR . '/Nette/loader.php';

// Configure application
$configurator = new NConfigurator;

// Enable Nette Debugger for error visualisation & logging
NDebugger::$logDirectory = dirname(__FILE__) . '/../log';
NDebugger::$strictMode = TRUE;
NDebugger::$email = '<EMAIL>';
// Pokud jsme v dev režimu, povolíme debugger pro všechny IP adresy
if (!IS_PRODUCTION) {
  NDebugger::enable(true); // V dev režimu povolíme debugger pro všechny IP adresy
} else {
  NDebugger::enable(array('************','127.0.0.1','::1')); // V produkčním režimu jen pro vybrané IP adresy
}


// Enable RobotLoader - this will load all classes automatically
$configurator->setTempDirectory(dirname(__FILE__) . '/../temp');
$configurator->createRobotLoader()
  ->addDirectory(APP_DIR)
  ->addDirectory(LIBS_DIR)
  ->register();

// Create Dependency Injection container from config.neon file
$configurator->addConfig(dirname(__FILE__) . '/config/config.neon');
$container = $configurator->createContainer();

//Get and setup a front controller
$application = NEnvironment::getApplication();

$dbsettings = NEnvironment::getConfig('database');
dibi::connect((array)$dbsettings, 'live');

/*
NRoute::setStyleProperty('presenter', NRoute::FILTER_TABLE, array(
    'zbozi' => 'Product',
    'zakaznik' => 'User',
    'kosik' => 'Basket',
    'kategorie' => 'Catalog',
    'text' => 'Page',
));

NRoute::setStyleProperty('action', NRoute::FILTER_TABLE, array(
    'zbozi' => 'products',
    'novy' => 'add',
    'prihlasit' => 'login',
    'objednat' => 'makeOrder',
));
*/

NRoute::$defaultFlags = NRoute::SECURED;

//Setup application router
$router = $application->getRouter();

$router[] = new NRoute('index.php', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
), NRoute::ONE_WAY);

// Admin
$router[] = new NRoute('administrace/<presenter>/<action>', array(
  'module' => 'Admin',
  'presenter' => 'Admin',
  'action' => 'default',
));

// routa pro detail katalogu
$router[] = new NRoute('[<key1>/]<key2>/k<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detail',
));

// routa pro detail zbozi
$router[] = new NRoute('[<key1>/]<key2>/p<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
));


// routa pro textove stranky
$router[] = new NRoute('text-<id>', array(
    'module' => 'Front',
    'presenter' => 'Page',
    'action' => 'detail',
));

// routa pro uzivatele
$router[] = new NRoute('user/<action>[/<id>]', array(
    'module' => 'Front',
    'presenter' => 'User',
    'action' => 'default',
));

//$router[] = new NSimpleRouter('Front:Homepage:default');

// routa pro detail zbozi a katalogu
$router[] = new NRoute('<presenter>/<key>/<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
), NRoute::ONE_WAY);

// deklarace obecné dvousměrné routy s cool-url tvarem
$router[] = new NRoute('<presenter>/<action>[/<id>]', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
    'id' => NULL,
));

/*
//nastaveni permission
$acl  = new NPermission();

//nastaveni roli
$acl->addRole('guest');
$acl->addRole('admin');

//nastaveni zdroju
$acl->addResource('Admin:Admin');
$acl->addResource('Admin:User');
$acl->addResource('Admin:Catalog');
$acl->addResource('Admin:Product');
$acl->addResource('Admin:Export');
$acl->addResource('Admin:Import');
$acl->addResource('Admin:Order');
$acl->addResource('Admin:DeliveryMode');
$acl->addResource('Admin:Manufacturer');
$acl->addResource('Admin:Page');
$acl->addResource('Admin:Config');

//nastaveni prav
$acl->allow('guest', 'Admin:Admin', array('login', 'logout', 'default'));
$acl->allow('admin', NPermission::ALL, NPermission::ALL);

NEnvironment::getUser()->setAuthorizator($acl);
*/

NFormContainer::extensionMethod('NFormContainer::AddNumeric', array('NumericInput', 'addNumericInput'));

$application->errorPresenter = 'Front:Error';
$application->catchExceptions = IS_PRODUCTION;

//Run the application!
$application->run();