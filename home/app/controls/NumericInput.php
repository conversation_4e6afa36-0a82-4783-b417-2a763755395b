<?php



/**
 * NumericInput input control.
 *
 * <AUTHOR>
 * @package    Nette\Extras
 */
class NumericInput extends /*Nette\Forms\*/NTextInput
{
        /*
         * Group separator
         * Example: 1,000.00 - there it is ,
         * @var string
         */
        static public $groupSeparator = "";

        /*
         * Decimal separator
         * Example: 1,000.00 - there it is .
         * @var string
         */
        static public $decimailSeparator = ",";

        /*
         * Accuracy
         * Example: 1,000.00 - there it is 2
         * @var string
         */
        static public $decimals = 2;


        protected static function _fixString($string) {
                $string = str_replace(self::$groupSeparator, "", $string);
                $string = str_replace(self::$decimailSeparator, ".", $string);// Do amerického formátu
                return $string;
        }

        /**
         * @param  string  label
         * @param  int  width of the control
         * @param  int  maximum number of characters the user may enter
         */
        public function __construct($label = NULL, $cols = NULL, $maxLenght = NULL)
        {
                parent::__construct($label, $cols, $maxLenght);
                $this->control->class = array('number');
                $this->addFilter("NTextInput::filterFloat");
        }
        
        public static function addNumericInput(NFormContainer $form, $name, $label, $cols = NULL, $maxLenght = NULL)
        {
          return $form[$name] = new self($label, $cols, $maxLenght);
        }

        /**
         * Getts value
         * @return float
         */
        public function getValue() {
                return (float)parent::getValue();
        }

        /**
         * Generates control's HTML element.
         * @return Html
         */
        public function getControl()
        {
                $control = parent::getControl();
                $control->value = number_format($this->getValue(), self::$decimals, self::$decimailSeparator, self::$groupSeparator);
                return $control;
        }

        public static function validateSmaller(NIFormControl $control, $num)
        {
            return ($control->getValue()<$num);
        }


        public static function validateBigger(NIFormControl $control, $num)
        {
            return ($control->getValue()>$num);
        }

}