<?php
class MyTranslator implements ITranslator {
  private $lang; // Aktualni jazyk
  private $debugMode = FALSE; 
  private $dic = array();
  /**
   * Translates the given string.
   * @param  string   message
   * @param  int      plural count
   * @return string
   */
  
  public function __construct($lang, $dic) {
    $this->dic = $dic;
    $this->lang = $lang;
  }
     
  public function translate($message, $count = NULL) {
    if ($this->lang == 'cs') return $message;
    if (array_key_exists($message, $this->dic)) return $this->dic[$message];
    return '#'.$message;
  }
}  
?>
