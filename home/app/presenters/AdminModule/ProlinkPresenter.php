<?php

final class Admin_ProlinkPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function editFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      
      $prolink = new ProlinksModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
  
      try {
        if ($id > 0) {
          $prolink->update($id, $vals);  
        } else {
          $id = $prolink->insert($vals);
        }
        $this->flashMessage('Uloženo v pořádku');
        $this->redirect('Prolink:edit', $id);        
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $prolink = new ProlinksModel();
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM prolinks ORDER BY pliname");  
    //ciselnik statusu
    $this->template->enum_plistatus = $prolink->getEnumPliStatus();
  }
  
  public function renderEdit($id) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $prolink = new ProlinksModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $prolink->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id;   
    }
  }
  
  public function actionDelete($id) {
    if ($id > 0) {
      $prolinks = new ProlinksModel(); 
      $prolinks->delete($id);
      $this->flashMessage('Záznam byl vymazán.');
    }
    $this->redirect('default');           
  }
  
  public function renderCheck() {
    $rows = dibi::fetchAll("SELECT * FROM prolinks WHERE plistatus=0 ORDER BY pliname");
    $log = array();
    foreach ($rows as $row) {
      $html = @file_get_contents($row->pliurlcheck);
      if ($html===false) {
        $log[$row->pliurlcheck] = "Chyba při načítání stránky"; 
      } else if (empty($html)) {  
        $log[$row->pliurlcheck] = "Prázdná stránka";
      }else{
        $pos = strpos($html, strtolower($this->config["SERVER_NAMESHORT"])); 
        $log[$row->pliurlcheck] = ($pos > 0 ? "OK": strtolower($this->config["SERVER_NAMESHORT"])."- nenalezeno");
      }
    }
    $this->template->log = $log;
  }
  
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');
    
    $prolink = new ProlinksModel();
    
    $form = new NAppForm();
    
    $form->addText('pliname', 'Název:', 30)
      ->addRule(NForm::FILLED, 'Název: je nutné vyplnit');

    $form->addText('pliurl', 'URL:', 30)
      ->addRule(NForm::FILLED, 'URL: je nutné vyplnit');
      
    $form->addText('pliurlcheck', 'URL kde je odkaz:', 30)
      ->addRule(NForm::FILLED, 'URL kde je odkaz: je nutné vyplnit');  
    
    $form->addTextArea('plidesc', 'Popis:', 60, 10);
          
    $form->addSelect('plistatus', 'Status:', $prolink->getEnumPliStatus());
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}