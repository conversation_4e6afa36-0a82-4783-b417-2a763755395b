<?php

final class Admin_ProductPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  /** @persistent */
  public $sCode = '';
  
  /** @persistent */
  public $sCode2 = '';
  
  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sCatId = '';
  
  /** @persistent */
  public $sCatId2 = '';

  /** @persistent */
  public $sManId = '';
  
  /** @persistent */
  public $sOrderBy = 'proname';
  
  /** @persistent */
  public $sOrderByType = 'ASC';

  public function productEditFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      
      $product = new ProductsModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
    
      //vezmu si obrazek pokud byl odeslan
      $image0 = null;
      $image1 = null;
      $file1 = null;
      if ($vals["pic0"]->isOk()) {
        $image0 = $vals["pic0"]->toImage();
      }
      if ($vals["pic1"]->isOk()) {
        $image1 = $vals["pic1"]->toImage();
        $image1_position = $vals["pic1position"];
      }
      if ($vals["attAdd"]->isOk()) {
        $file1 = $vals["attAdd"];
        $filedesc = $vals["ataname"];
      }
      
      
      //pokud neni vyplneny nazev obrazku, vyplnim kod zbozi
      if (empty($vals["propicname"])) $vals["propicname"] = $vals["procode"];
      
      //projdu formularova pole a ktere nejsou treba odstranim
      $catIds = array();
      $proParam = array();
      foreach ($vals as $key => $value) {
        //zarazeni do katalogu
        if (substr($key, 0, 4) === 'cat_') {
          if (($key == 'cat_0' || $key == 'cat_') && $value > 0) {
            //nove zarazeni
            $catIds[] = $value;
          } else if ($value) {
            //stavajici zarazeni
            $catIds[] = substr($key, 4);
          }  
          unset($vals[$key]);
        }
        //parametry zbozi
        if (substr($key, 0, 10) == "param_name" && $value != "") {
          $arr = explode("_", $key);
          $proParam[$arr[2]] = array('prpname' => $value, 'prpvalue' => $vals["param_value_".$arr[2]]);
        }
        
        if (substr($key, 0, 3) != "pro") unset($vals[$key]);
      }                                         
      
      //pokud se ma ulozit jako nova polozka
      $saveAsNewSrcProId = 0;
      if ($id > 0) {
        if ($form['saveAsNew']->isSubmittedBy()) {
          $saveAsNewSrcProId = $id;
          $id = 0;
        }  
      }
      $isnew = ($id == 0);  
      try {
        if ($product->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          
          //upravim a ulozim hlavni a doplnkovy obrazek
          $sizes = (array($this->config["PROPICSIZE_BIG"].'xbig', $this->config["PROPICSIZE_DETAIL"].'xdetail', $this->config["PROPICSIZE_LIST"].'xlist'));
          if (isset($image0)) $this->saveImage($image0, NEnvironment::getVariable('wwwDir')."/pic/product", $vals["propicname"].".jpg", $sizes); 
          if (isset($image1)) $this->saveImage($image1, NEnvironment::getVariable('wwwDir')."/pic/product", $vals["propicname"]."_".$image1_position.".jpg", $sizes); 
          //ulozim prilohu
          if (isset($file1)) {
            //ulozim do db
            $atts = new AttachmentsModel();
            $ataVals = array(
              'ataproid'=>$id,
              'ataname'=>$filedesc,
              'atafilename'=>NStrings::webalize($filedesc).'_'.$id.'.'.substr($file1->getName(), -3),
              'atasize'=>(string)$file1->getSize(),
              'atatype'=>substr($file1->getName(), -3),
            );
            $atts->insert($ataVals);
            $file1->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
          }
          //zpracovani zarazeni do katalogu
          if ($id > 0) $product->updateCatPlace($id, $catIds);
          
          //zpracovani parametru
          $proparams = new ProParamsModel();
          if ($saveAsNewSrcProId > 0) {
            $params = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=%i", $saveAsNewSrcProId, " ORDER BY prpid");
            foreach ($params as $row) {
              unset($row->prpid);   
              unset($row->prpdatec);   
              unset($row->prpdateu);
              $row->prpproid = $id;
              $proparams->insert($row);      
            }
          } else {
            foreach ($proParam as $key => $value) {
              $proparams->save($key, array('prpproid' => $id, 'prpname' => $value["prpname"], 'prpvalue' => $value["prpvalue"]));   
            }
          }
          
          //prilohy
          if ($saveAsNewSrcProId > 0) {
            $files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $saveAsNewSrcProId);
            $ata = new AttachmentsModel();
            foreach ($files as $row) {
              unset($row->ataid);   
              unset($row->atadatec);   
              unset($row->atadateu);
              $row->ataproid = $id;
              $ata->insert($row);      
            }
          }
          
          //katalogova cesta aktualizace
          $product->genProKeyMaster($id); 
          
          //redirect podle tlacitka
          $btn = $form->isSubmitted();
          $btn_name = $btn->name;
          if ($isnew) $btn_name = "newitem";
          switch ($btn_name) {
             case 'save':
               $this->redirect('default');
               break;
             case 'saveAsNew':
              $this->redirect("this");
               break;
             default:
               $this->redirect("edit", array('id'=>$id, 'tab'=>$btn_name));
               break;
          }         
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  }
  
  public function productListEditFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      
      $products = new ProductsModel();
      $vals = $form->getValues();
      foreach ($vals as $id => $item) {
        if ($item['proaccess']!="") {
          $products->update($id, $item);  
        }  
      }
      $this->redirect('default');
    }  
  }
  
  public function productBatchUpdateFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $proPrice = (int)$vals["proprice"];
      $prcCat = $vals["prcid"];
      $sql = "UPDATE products SET proprice$prcCat=".$proPrice." WHERE proid IN (SELECT capproid FROM catplaces INNER JOIN catalogs ON (capcatid=catid) WHERE capcatid=".$vals["catid"]." AND catpathids LIKE '|".$vals["catid"]."|%')";
      if (dibi::query($sql)) {
          $this->flashMessage("Ceny byly aktualizovány");
         $this->redirect('this');
      } else {
        $form->addError("Nastala chyba při aktualizaci ceny");
      }
      
    }  
  }
   
  
  /********************* view default *********************/ 
  
  private function getDataSource() {
    $product = new ProductsModel();
    
    $where = "";
    if (!empty($this->sCode)) $where .= " procode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sCode2)) $where .= " procode2 LIKE '%$this->sCode2%' AND ";
    if (!empty($this->sName)) $where .= " proname LIKE '%$this->sName%' AND ";
    if (!empty($this->sCatId)) $where .= " proid IN (SELECT capproid FROM catplaces WHERE capcatid=$this->sCatId) AND ";
    if (!empty($this->sCatId2)) $where .= " proid IN (SELECT capproid FROM catplaces WHERE capcatid=$this->sCatId2) AND ";
    if (!empty($this->sManId)) $where .= " promanid =$this->sManId AND ";
    
    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);    
    }
    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY proname";
    }  
  
    return $product->getDataSource("SELECT proid, procode, prokey, prokeymaster, procode2, proname, prostatus, propricea, manname, proaccess, proorder, coalesce(prscnt, 0) AS prscnt, probigsize, prooffer 
FROM products 
LEFT JOIN manufacturers ON (promanid=manid)
LEFT JOIN products_salestat ON (prsproid=proid)  
$where
group by proid 
$orderBy");
  }
  
  public function renderDefault($level = 0) {
    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page; 
    $this->template->dataRows = $dataRows;           

    //ciselnik statusu
    $product = new ProductsModel();
    $this->template->enum_prostatus = $product->getEnumProStatus();
  }
  
  public function renderEdit($id, $catId=0) {
    $form = $this['productEditForm'];
    $dataRow = false;
    if (!$form->isSubmitted()) {
      
      $product = new ProductsModel();
      if ($id > 0) {
        $dataRow = $product->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      } else {
        if ($catId > 0) {
          $form->setDefaults(array('cat_0'=>$catId));
        }
      }
      $this->template->id = $id;
    }
    $this->template->dataRow = $dataRow;     
    
  }
  
  public function actionDeleteParam($proid, $parid) {
    if ($parid > 0) {
      $proParam = new ProParamsModel(); 
      $proParam->delete($parid);
      $this->flashMessage('Parametr byl vymazán'); 
    }
    $this->redirect('Product:edit#tabs_param', $proid);           
  }
  
  public function actionRecalcSaleStat() {
    $products = new ProductsModel();
    $products->recalcSaleStat();
    $this->flashMessage("Statistika prodejnosti byla přepočítána");
    $this->redirect('Admin:default');
  }
  
  public function actionDeleteImage($proid, $filename) {
    //vymazu jen pokud neni stejny obrazek pouzit u jinych produktu
    $fileBody = dibi::fetchSingle("SELECT propicname FROM products where proid=%i", $proid);
    $cnt = dibi::fetchSingle("SELECT COUNT(*) FROM products where propicname=%s", $fileBody, " AND proid!=%i", $proid);
    if ($cnt == 0) {
      @unlink(NEnvironment::expand('%wwwDir%/pic/product/list/').$filename);
      @unlink(NEnvironment::expand('%wwwDir%/pic/product/detail/').$filename);
      @unlink(NEnvironment::expand('%wwwDir%/pic/product/big/').$filename);
    } else {
      $this->flashMessage("Obrázek nejde vymazat, je použit u jiných produktů. Pokud nechcete sdílet obrázky s jiným produktem, zadejte jiný Název obrázku:", 'err');
    }  
    $this->redirect('Product:edit#tabs_pic', $proid);           
  }

  public function actionSearchAc($term) {
    $this->template->rows = dibi::fetchAll("SELECT * FROM catalogs WHERE catname LIKE %~like~", $term, " order by catpath");
  }

  public function actionDelete($proid) {
    $products = new ProductsModel();
    $products->delete($proid);
    $this->redirect('Product:default');           
  }
  
  public function actionDeleteFile($ataid, $proid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);
      
      //soubor vymazu jen pokud neexstuje v jine priloze
      $filesCnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM attachments WHERE atafilename=%s", $file->atafilename);
      if ($filesCnt == 0) @unlink(NEnvironment::expand('%wwwDir%/files/').$file->atafilename);
    }
    $this->redirect('Product:edit#tabs_attachment', $proid);           
  }
  
  public function searchFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) { 
        $this->sCode = Null;
        $this->sCode2 = Null;
        $this->sName = Null;
        $this->sCatId = Null;
        $this->sCatId2 = Null;
        $this->sManId = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {  
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sCode2 = $vals["code2"];
        $this->sName = $vals["name"];
        $this->sCatId = $vals["catid"];
        $this->sCatId2 = $vals["catid2"];
        $this->sManId = $vals["manid"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }   
    }
    $this->redirect("Product:default");  
  }
  
  /********************* facilities *********************/

  protected function createComponentListEditForm() {
    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    
    $form = new NAppForm();
    foreach ($dataRows as $row) {
      $cont = $form->addContainer($row->proid);
      $cont->addText('proaccess', '', 3)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::INTEGER, "hodnota musí být celé číslo"); 
    }
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = callback($this, 'productListEditFormSubmitted');
    return $form;
  }  
  
  protected function createComponentProductEditForm() {
    $id = (int)$this->getParam('id');
    
    $product = new ProductsModel();
    if ($id > 0) $dataRow = $product->load($id);
    
    $form = new NAppForm();
    
    $form->addGroup()->setOption('container', NHtml::el('div')->id('tabs_editmain'));
    $form->addText('procode', 'Katalogové číslo:', 30)
      ->addRule(NForm::FILLED, 'Prosím vyplňte Katalogové číslo.');
    $form->addText('procode2', 'Objednávací číslo:', 30);  
      
    $form->addText('proname', 'Název:', 100)
      ->addRule(NForm::FILLED, 'Název: je nutné vyplnit');
  
    $form->addSelect('promanid', 'Výrobce:', $product->getEnumProManId());
    
    if ($id > 0) {
     $form["promanid"]->setOption("description", NHtml::el('')->setHtml('[ <a href="'.$this->link('Manufacturer:edit', $dataRow->promanid).'">upravit<a/> ]'));
    }

    $form->addSelect('protypid', 'Typ:', $product->getEnumProTypId());
    
    $form->addTextArea('prodescs', 'Krátký popis:', 100, 4)
      ->addRule(NForm::FILLED, 'Krátký popis: je nutné vyplnit.');  
    
    $form->addNumeric('propricea', 'Cena prodej:', 15)
      ->addRule(NForm::FILLED, 'Cena prodej: je nutné vyplnit.')
      ->addRule(NForm::FLOAT, "Cena prodej: hodnota musí být číslo");
    
    $form->addNumeric('propriceb', 'Cena dealer:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Cena dealer: hodnota musí být číslo"); 
        
    $form->addNumeric('propricec', 'Cena nákup:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Cena nákup: hodnota musí být číslo"); 
    
    $form->addTextArea('pronoteint', 'Poznámka neviditelná:', 100, 3);
        
    /*
    $form->addNumeric('propriced', 'Cena DEALER:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Cena DEALER: hodnota musí být číslo"); 
    
    $form->addNumeric('propricecom', 'Cena běžná:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Cena běžná: hodnota musí být číslo");
    */
    
    $form->addSelect('provatid', 'Sazba DPH:', $product->getEnumProVatId());                      
    
    /*
    $form->addText('procredit', 'Kredity:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::NUMERIC, "Kredity: hodnota musí být číslo");
        
    $form->addNumeric('prorecycle', 'Recyklační poplatek:', 15)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Recyklační poplatek: hodnota musí být číslo");
    */
    
    $form->addText('proaccess', 'Skladem:', 30)
      ->setOption('description', 'Počet dní do dodání zboží, 0 - skladem, 90 - na dotaz, 99 - dostupnost se nezobrazí');
      
    $form->addText('proorder', 'Pořadí:', 30)
      ->setOption('description', 'Číslo podle kterého je možno řadit zboží v katalogu');    
    
    $form->addNumeric('proweight', 'Hmotnost:', 15)
      ->setOption('description', 'Kg')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::FLOAT, "Hodnota musí být číslo");
        
    $form->addSubmit('tabs_editmain', 'Uložit')->getControlPrototype()->class('default');        
    
    //zarazeni do katalogu
    $group = $form->addGroup("")->setOption('container', NHtml::el('div')->id('tabs_editcatalog'));
    $cnt = 0;
    $cat = new CatalogsModel();
    if ($id > 0) {
      //nactu aktualni zarazeni s chkboxem - korenove urovne
      $result = dibi::query("SELECT catid, capid, catname, catpath FROM catalogs INNER JOIN catplaces ON (capcatid=catid) WHERE capproid=$id");
      foreach ($result as $n => $row) {
        $cnt++;
        $form->addCheckbox("cat_".$row["catid"], ' '.str_replace('|', ' > ', $row["catpath"]))
          ->setDefaultValue(true);
      }
    }

    $form->addSelect('cat_0', '', $cat->getEnumCatalogCombo(0))
      ->setPrompt('Přiřadit další druh výrobku ...');

    if ($cnt === 0) {
      $group->setOption('description', 'Zboží zatím není zařazeno v katalogu!');
    } else {
      $group->setOption('description', 'Aktuální zařazení');
    }
    
    
    
    $form->addSubmit('tabs_editcatalog', 'Uložit')->getControlPrototype()->class('default');
    
    //detailni popis
    $form->addGroup()->setOption('container', NHtml::el('div')->id('tabs_editdesc')); 
    $form->addTextArea('prodesc', '', 130, 15);
    $form['prodesc']->getControlPrototype()->class('mceEditor'); 
    
    $form->addSubmit('tabs_editdesc', 'Uložit')->getControlPrototype()->class('default'); 
   
    //SEO
    $form->addGroup()->setOption('container', NHtml::el('div')->id('tabs_seo')); 
    $form->addText('prokey', 'URL klíč:', 30)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu zboží');  
    
    $form->addText('protitle', 'Title:', 80)
      ->setOption('description', 'Zadávejte pokud chcete jiné TITLE nez je název zboží');  
    
    $form->addText('prokeywords', 'Klíčové slova:', 30)
      ->setOption('description', 'Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený čárkou');    
   
    $form->addTextArea('prodescription', 'Description', 100, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', NHtml::el('p')->class('charsRemaining'));
    
    $form->addSubmit('tabs_seo', 'Uložit')->getControlPrototype()->class('default');
    
    //ostatni udaje
    $form->addGroup()->setOption('container', NHtml::el('div')->id('tabs_editrem')); 

    $form->addCheckbox('probigsize', 'Nadrozměrné zboží');    
    $form->addCheckbox('prooffer', 'Poptávkové zboží');    
    
    $form->addText('pronames', 'Název pro vyhledávače:', 100); 
    $form->addCheckbox('progoogleoff', 'Neposílat do google nákupy');    
    
    $form->addText('prowarranty', 'Záruka:', 30)
      ->setOption('description', 'Textově délka záruky, např. "24 mesíců"');
    
    $form->addText('protrarestr', 'Omezení dopravy:', 30)
      ->setOption('description', 'Zadávejte výčet ID doprav oddělený čářkou, které nejsou vhodné pro toto zboží');  
    
    $form->addSelect('prostatus', 'Status:', $product->getEnumProStatus());
    $form->addSubmit('tabs_editrem', 'Uložit')->getControlPrototype()->class('default');      
  
    //obrazky
    $emptypos = 0;
    if ($id > 0) {
      //nactu seznam obrazku
      $images = "";
      $baseUri = (string)$this->getHttpRequest()->url->baseUrl;
      for($piccnt=0;$piccnt<=10;$piccnt++){
        if ($piccnt == 0) {
          $title = "Hlavní obrázek";
          $filename = $dataRow->propicname.'.jpg';
        } else {
          $title = $piccnt.". pozice";
          $filename = $dataRow->propicname.'_'.$piccnt.'.jpg';
        }
        
        if (file_exists(NEnvironment::expand('%wwwDir%/pic/product/list/').$filename)) {
          $images .= '
  <img title="'.$title.'" src="'.$baseUri.'/pic/product/list/'.$filename.'?'.time().'" />';
          if ($piccnt != 0) $images .= '<a href="'.$this->link('deleteImage', $id, $filename).'"><img src="'.$baseUri.'/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat '.$title.'" /></a>';
        } else {
          if (empty($emptypos)) $emptypos = $piccnt;
        }
      }
      
      //doplnim seznam produktu ktere maji stejny nazev obrazku
      $duplImg = "";
      if ($dataRow) {
        $proSamePicName = dibi::fetchAll("SELECT * FROM products WHERE propicname=%s", $dataRow->propicname, " AND proid!=%i", $dataRow->proid);
        foreach ($proSamePicName as $row) {
          $duplImg .= '<a href="'.$this->link('edit', $row->proid).'">['.$row->procode.'] '.$row->proname.'</a><br />';  
        }
      }
      if (!empty($duplImg)) $duplImg = '<p><strong>Nalezena duplicita názvů obrázků: </strong> <a href="#" onclick="return zobrazSkryj(\'toggle\');">vypsat</a><br /><span id="toggle" style="display:none">'.$duplImg.'</span></p>';   
    }
    if (isset($images) || !empty($duplImg)) {
      $form->addGroup()
        ->setOption('container', NHtml::el('div')->id('tabs_pic'))
        ->setOption('description', NHtml::el('div')->setHtml($images.$duplImg));
    } else {
      $form->addGroup()
        ->setOption('container', NHtml::el('div')->id('tabs_pic'));
    }
    
    $form->addText('propicname', 'Název obrázku:', 30)
      ->addRule(NForm::REGEXP, 'Název obrázku: můžete vyplnit pouze malá a velká písmena, pomlčku a podtržítko', '/^[a-z0-9_ .-]*$/i')
      ->setOption('description', '.jpg');  
    
    //obrazek hlavni
    $form->addUpload('pic0', 'Hlavní obrázek:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.') 
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');
    
    //obrazek dalsi
    $form->addUpload('pic1', 'Další obrázek:') 
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');  
    
    for($i=1;$i<=10;$i++) $arri[$i]=$i;
    $form->addSelect('pic1position', 'Pozice obrázku:', $arri)
      ->setDefaultValue($emptypos)
      ->setOption('description', 'Nahráním nového obrázku na obsazenou pozici ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.') ;      
    
    //$form->addTextArea('proaudio', 'Audio soubory (Titulek;velikost souboru;název souboru):', 100, 4);  
    
    $form->addSubmit('tabs_pic', 'Uložit')->getControlPrototype()->class('default');
        
    //prilohy
    $attachments = "";
    if ($id > 0) {
      //nactu aktualni prilohy
      $atts = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $id);
      foreach ($atts as $row) {
        $attachments .= '
  <a href="'.$baseUri.'/files/'.$row["atafilename"].'?'.time().'">'.$row["ataname"].'</a> <a href="'.$this->link('deleteFile', $row["ataid"], $id).'"><img src="'.$baseUri.'/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat '.$row["ataname"].'" /></a><br />';
      }
    }
    if (isset($attachments)) {
      $form->addGroup()
        ->setOption('container', NHtml::el('div')->id('tabs_attachment'))
        ->setOption('description', NHtml::el('div')->setHtml($attachments));
    } else {
      $form->addGroup()
        ->setOption('container', NHtml::el('div')->id('tabs_attachment'));
    }
    //nova priloha
    $form->addUpload('attAdd', 'Nová příloha:');
    $form->addText('ataname', 'Název přílohy:', 100) 
      ->addConditionOn($form["attAdd"],NForm::FILLED)
        ->addRule(NForm::FILLED, 'Vyplňte název přílohy');  
        
    $form->addSubmit('tabs_attachment', 'Uložit')->getControlPrototype()->class('default');
    
    //parametry zbozi
    $form->addGroup()->setOption('container', NHtml::el('div')->id('tabs_param'));
    
    //novy parametr
    $form->addText('param_name_0', 'Nový parametr - název:', 80);
    $form->addText('param_value_0', 'Nový parametr - hodnota:', 80)
      ->addConditionOn($form["param_name_0"], NForm::FILLED)
        ->addRule(NForm::FILLED, 'Nový parametr - hodnota: je nutné vyplnit.');
    
    //stavajici parametry
    if ($id > 0) {
      $rs = dibi::query("SELECT * FROM proparams WHERE prpproid=$id ORDER BY prpid");
      foreach ($rs as $row) {
        $form->addHidden('param_name_'.$row->prpid)
          ->setDefaultValue($row->prpname);
        $form->addText('param_value_'.$row->prpid, $row->prpname.":", 80)
          ->setOption('description', NHtml::el('span')->setHtml('<a href="'.$this->link('deleteParam', $id, $row->prpid).'"><img src="/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat" /></a>'))
          ->setDefaultValue($row->prpvalue)
          ->addConditionOn($form['param_name_'.$row->prpid], NForm::FILLED)
            ->addRule(NForm::FILLED, 'Parametr - hodnota: je nutné vyplnit.');
      }
    }
    
    $form->addSubmit('tabs_param', 'Uložit')->getControlPrototype()->class('default');
    
    $form->addGroup()->setOption('container', NHtml::el('div')->id('save_button'));
    $form->addSubmit('save', 'Uložit a vrátit se na seznam')->getControlPrototype()->class('default');
    if ($id > 0) $form->addSubmit('saveAsNew', 'Uložit jako novou položku')->getControlPrototype()->class('default');
    
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'productEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    //$form->setRenderer(new ScaffoldingRenderer);
    
    return $form;  
  }
  
  protected function createComponentSearchForm() {
    $orders = new OrdersModel();
    $products = new ProductsModel();
    $catalogs = new CatalogsModel(); 
    
    $form = new NAppForm();
    $form->getElementPrototype()->novalidate = 'novalidate';  
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Katalogové č.", 10)
      ->setDefaultValue($this->sCode);  
    $form->addText("code2", "Objednávací č.", 10)
      ->setDefaultValue($this->sCode2);    
    $form->addText("name", "Název zboží", 10)
      ->setDefaultValue($this->sName);


    $form->addSelect("catid2", "Katalog", $catalogs->getEnumCatalogCombo())
      ->setDefaultValue($this->sCatId2);

    $form->addText("catid", "Katalog")
      ->setHtmlId("catid_search");

    $form->addText("catname", "", 60)
      ->setHtmlId("catname_search");

    $catPath= "";
    if ($this->sCatId > 0) {
      $form["catid"]->setDefaultValue($this->sCatId);
      //načteme katalogogvou cestu
      $cat = $catalogs->load($this->sCatId);
      $catPath = str_replace('|', ' > ', $cat->catpath);
      $form["catname"]->setDefaultValue($catPath);
    }

    
    $form->addSelect("manid", "Výrobce", $products->getEnumProManId())
      ->setPrompt("")
      ->setDefaultValue($this->sManId);
    
    $arr = array(
      'proname'=>'Název zboží',
      'procode'=>'Katalogové číslo',
      'procode2'=>'Objednací číslo',
      'prscnt'=>'Prodejnosti',
      'proorder'=>'Pořadí',
    ); 
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);
    
    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    ); 
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);
                    
    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }
  
  protected function createComponentProductBatchUpdateForm() {
    $catalogs = new CatalogsModel(); 
    
    $form = new NAppForm();  
    $form->addSelect("catid", "Katalog", $catalogs->addEnumCatalogRootLevel())
      ->addRule(NForm::FILLED, 'Zařazení do katalogu musí být vyplněno');
    $form->addSelect("prcid", "Cenová hladina", array('a'=>'cena A', 'b'=>'cena B','c'=>'cena C','d'=>'cena D'))
      ->addRule(NForm::FILLED, 'Cenová hladina musí být vyplněna');  
                    
    $form->addText("proprice", "Nová cena")
      ->addRule(NForm::FILLED, 'Nová cena musí být vyplněna');              
    $form->addSubmit('update', 'Aktualizovat');
    $form->onSuccess[] = array($this, 'productBatchUpdateFormSubmitted');
    return $form;
  }
  
  protected function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  }

  public function actionDetachImages($proid) {

    $this->detachImages($proid);
    $this->flashMessage("Obrázky byly odpárované a nyní má produkt samostatnou kopii sady obrázků.");
    $this->redirect("edit", $proid);

  }

  protected function detachImages($proId) {
    $pros = new ProductsModel();
    $pro = $pros->load($proId);

    $baseDir = WWW_DIR . "/pic/product/";
    $baseSrcName = $pro->propicname;
    $baseTargetName = NStrings::webalize($pro->procode);

    if ($baseSrcName === $baseTargetName) {
      $baseTargetName .= "-" . $proId;
    }

    $dirs = array('list', 'detail', 'big');
    foreach ($dirs as $dir) {
      for ($i = 0; $i <= 10; $i++) {
        $sufix = $i === 0 ? "" : "_" . $i;
        $fileSrcFullPath = $baseDir . "/" . $dir . "/" . $baseSrcName . $sufix . ".jpg";
        $fileTargetFullPath = $baseDir . "/" . $dir . "/" . $baseTargetName . $sufix . ".jpg";
        if (file_exists($fileSrcFullPath)) {
          copy($fileSrcFullPath, $fileTargetFullPath);
        }
      }
    }

    $vals = ["propicname" => $baseTargetName];
    $pros->update($proId, $vals);
  }
}