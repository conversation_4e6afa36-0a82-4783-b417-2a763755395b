<?php

final class Admin_ConfigPresenter extends Admin_BasePresenter {
  
  /** @persistent */
  public $backlink = '';

  public function formConfigSubmitted(NAppForm $form) {
    if ($form->isSubmitted()) {
      $config = new ConfigModel();
      $values = $form->getValues();
      foreach ($values as $key => $value) {
        $arr = explode('_', $key);
        if ($arr[0]==='id') {
          $config->update($arr[1], array('cfgvalue'=>$value));
        }    
      }
      //vymazu cache
      $cache = NEnvironment::getCache('app'); 
      unset($cache['config']);
      $this->flashMessage('Uloženo v pořádku');
      //redirect podle tlacitka
      $btn = $form->isSubmitted();
      $btn_name = $btn->name;
    }
    $this->redirect('default',  array('tab'=>$btn_name));
  }
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
           
  }
  
  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentConfigForm() {
    $config = new ConfigModel();
    $form = new NAppForm;
    
    // setup custom rendering
    $renderer = $form->getRenderer();
    $renderer->wrappers['label']['container'] = 'td';
    $renderer->wrappers['control']['container'] = 'th';
     
    $rows = dibi::fetchAll('SELECT * FROM config ORDER BY cfgtypid, cfgorder');
    
    $cfgtypid = 0;
    foreach ($rows as $row) {
      if ($cfgtypid !=  $row->cfgtypid) {
        if ($cfgtypid > 0) $form->addSubmit('tab_'.$cfgtypid, 'Uložit');
        $cfgtypid = $row->cfgtypid;
        $form->addGroup("")->setOption('container', NHtml::el('div')->id('tab_'.$cfgtypid));
      }
      switch ($row->cfgcontroltype) {
         case 'text':
           $form->addText('id_'.$row->cfgid, $row->cfgnote, 100)
             ->setDefaultValue($row->cfgvalue);
           break;
         case 'textarea':
           $form->addTextArea('id_'.$row->cfgid, $row->cfgnote, 100, 3)
             ->setDefaultValue($row->cfgvalue);
           break;  
         case 'combo':
           list($ids, $texts) = explode(';', $row->cfgvalues);
           $idsarr = explode(',', $ids); 
           $textsarr = explode(',', $texts);
           if (count($idsarr) == 0 || count($textsarr) == 0 || count($idsarr) != count($textsarr)) continue;
           $vals = array_combine($idsarr, $textsarr);
           $form->addSelect('id_'.$row->cfgid, $row->cfgnote, $vals)
             ->setDefaultValue($row->cfgvalue);
           break;
      }
    }
    $form->addSubmit('tab_'.$cfgtypid, 'Uložit');      
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'formConfigSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
}