<?php

final class Admin_OrderPresenter extends Admin_BasePresenter {

  /** @persistent */
  public $sCode = '';
  
  /** @persistent */
  public $sName = '';
  
  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';
  
  /** @persistent */
  public $sNotClosed =  true;
  
  /** @persistent */
  public $sOrderBy = 'orddatec';
  
  /** @persistent */
  public $sOrderByType = 'DESC';
  
  public function orderEditFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $orders = new OrdersModel();
      $id = $this->getParam('id');
      $values = $form->getValues();
      //nactu si objednavku pokud existuje
      $order = false;
      if ($id > 0) {
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
      }
      
      $orders->save($id, $values);  
      //kontrola zda nezmenil dopravu
      if ($values["orddelid"] != $order->orddelid) {
        //zmenila se doprava, aktualizuju v polozkach
        //nactu si zpusob dopravy
        $delivery = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $values["orddelid"]);
        //zjistim ID polozky dopravy
        $oriid = dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=1");
        $values = array(
          'oriname' => $delivery->delname,
          'oriprice' => $delivery->delprice,
        );
        $orditems = new OrdItemsModel();
        $orditems->update($oriid, $values);    
      }
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
    }  
    $this->redirect('edit', $id);
  }
  
  public function orderChangeStateFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {

      $mailIt = $form["newstate_mail"]->isSubmittedBy();

      $orders = new OrdersModel();
      $formVals = $form->getValues();
      $id = $this->getParam('id');
      //pokud storno vymazu cislo fa a datum vystaveni
      if ($formVals["ordstatus"] == 5) {
        $formVals["ordinvcode"] = Null;
        $formVals["ordinvdate"] = Null;
        $this->flashMessage('Byla vystornována fa, pokud existovala.');
      }
      $orders->update($id, $formVals);
      $orders->logStatus($id, $formVals["ordstatus"]);  
      $this->flashMessage('Změna stavu provedena v pořádku');
      if ($formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 4 || $formVals["ordstatus"] == 5) {
        //prepocitam statistiku prodejnosti
        $products = new ProductsModel();
        $products->recalcSaleStat() ;
        $this->flashMessage('Byla přepočtena statistika prodejnosti');
      }
      
      
      //mailuju zakaznikovi zmenu stavu jen pokud neni zarazen na CL
      if ($mailIt) {
        if ($formVals["ordstatus"] == 1 || $formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 5 || $formVals["ordstatus"] == 2 || $formVals["ordstatus"] == 6) {
          $mailTemplate = $this->createTemplate();
          $orderRow = $orders->load($id);
          $mailTemplate->orderRow = $orderRow;
          $delModes = new DeliveryModesModel();
          $mailTemplate->payMode = $delModes->load($orderRow->orddelid);
          $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
          if (!empty($orderRow->ordparcode)) {
            //zjistim URL
            $url = $mailTemplate->payMode->delurlparcel;
            $url = str_replace('#CODE#', $orderRow->ordparcode, $url);
            $mailTemplate->parcelURL = $url;
          }
          $mailTemplate->enum_ordStatus = $orders->getEnumOrdStatus();
          $mailTemplate->lang = $this->lang;
          $mailTemplate->setTranslator($this->translator);
          $mailTemplate->setFile(APP_DIR.'/../templates/Mails/mailOrderChanged.phtml');

          if ($formVals["ordstatus"] == 1 || $formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 5 || $formVals["ordstatus"] == 2 || $formVals["ordstatus"] == 6) {
            //mailuju zakaznikovi
            try {
              $this->mailSend($orderRow->ordmail, $mailTemplate->translate("Změna stavu objednávky č.")." ".$orderRow->ordcode, $mailTemplate);
            } catch (InvalidStateException $e) {
              $this->flashMessage("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")", "err");
            }
          }
        }
      }
    }  
    $this->redirect('edit', $id);
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $orders = new OrdersModel();
    $where = "";
    if (!empty($this->sCode)) $where .= " ordcode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sAdmin)) $where .= " ordadmid = ".$this->sAdmin." AND ";
    if (!empty($this->sName)) $where .= " (ordilname LIKE '%$this->sName%' OR ordstlname LIKE '%$this->sName%' OR ordstfirname LIKE '%$this->sName%' OR ordifirname LIKE '%$this->sName%') AND ";
    if ($this->sNotClosed) $where .= " ordstatus NOT IN (4,5,7) AND ";
    if ((string)$this->sStatus!='') $where .= " ordstatus=$this->sStatus AND ";  
   
    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);    
    }  
    
    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY orddatec DESC";
    }  
    $dataRows = dibi::fetchAll("
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, d.delprice AS delprice, admname,
    (SELECT orldatec FROM orders_log WHERE orlordid=ordid AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1) AS datesend,
    (SELECT MAX(oriprobigsize) FROM orditems WHERE oriordid=ordid) AS oriprobigsize,
    (SELECT MAX(oriprooffer) FROM orditems WHERE oriordid=ordid) AS oriprooffer
    FROM orders 
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid) 
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    $where
    $orderBy
    ");
    $this->template->dataRows = $dataRows;           
    
    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }
  
  public function renderAutocompleteProducts() {
    $term = $this->getParam('term');
    if (!empty($term)) $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, propricea FROM products WHERE proname LIKE '$term%' OR  procode LIKE '$term%'");
  }
  
  public function renderEdit($id) {
    $form = $this['orderEditForm'];
    $formSate = $this['orderChangeStateForm'];   
    
    if (!$form->isSubmitted() && !$formSate->isSubmitted()) {
      $orders = new OrdersModel();
      $dataRow = $orders->load($id);
      if (!$dataRow) {
        throw new NBadRequestException('Záznam nenalezen');
      }
      //naformatuju datum
      $form->setDefaults($dataRow);
      $formSate->setDefaults($dataRow);
    
      $this->template->dataRow = $dataRow;
       
      //doplnim polozky objednavky
      $this->template->ordItems = dibi::query("SELECT * from orditems where oriordid=%i", $dataRow->ordid)->fetchAssoc('oriid');
      
      //doplnim polozku se slevou
      $this->template->ordItemDisc = dibi::fetch("SELECT * from orditems where oriordid=%i AND oritypid=3", $dataRow->ordid);
      
      
      //doplnim zpusoby dodani
      $delModes = new DeliveryModesModel();
      $this->template->enum_delModes = $delModes->getEnumDelModes();
      
       //doplnim log zmen
      
      $this->template->statusLog = dibi::fetchAll("SELECT * FROM orders_log WHERE orlordid=%i", $dataRow->ordid);
      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
      
      //kontrola cerne listiny
      $this->template->bl = $orders->blAnalyse($dataRow);
      
    }
  }
  
  public function actionDelete($id) {
    if ($id > 0) {
      $orders = new OrdersModel(); 
      $orders->delete($id);
      $this->flashMessage('Záznam byl vymazán'); 
    }
    $this->redirect('default');           
  }
  
  public function actionMakeInvoice($id) {
    if ($id > 0) {
      $orders = new OrdersModel();
      try { 
        $orders->makeInvoice($id);
        $this->flashMessage('Faktura byla vystavena'); 
      } catch (ModelException $e) {
        $this->flashMessage($e->getMessage(), 'err');
      }    
    }
    $this->redirect('default');           
  }
  
  public function actionPrint($id) {
    if ($id > 0) {
      $this->printOrder($id, 'D'); 
    }
    //$this->redirect('default');           
  }
  
  public function actionMailParcelReminder($id) {
    $orders = new OrdersModel();
    $order = $orders->load($id); 
    if ($order) {
      //mailuju
      $mailTemplate = $this->createTemplate();
      $enums = new EnumcatsModel();
      $mailTemplate->enum_Countries = $enums->getEnumCountries();
      $mailTemplate->orderRow = $order;
      $mailTemplate->ordItemRows = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $order->ordid);
      //doplnim datum odeslani
      $mailTemplate->ordDateSend = dibi::fetchSingle("SELECT orldatec FROM orders_log WHERE orlordid=%i AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1", $order->ordid);
      $mailTemplate->setTranslator($this->translator);
      $mailTemplate->lang = $this->lang;
      $mailTemplate->setFile(APP_DIR.'/../templates/Mails/mailParcelReminder.phtml');
      try {
        //mailuju zakaznikovi
        $this->mailSend($order->ordmail, $this->translator->translate("Prosím vyzvedněte si zásilku, objednávka č.")." ".$order->ordid, $mailTemplate);
        $this->flashMessage("Informační email s upozorněním byl odeslán");
      } catch (InvalidStateException $e) {
        $this->flashMessage("Nepodařilo se ale odeslat informační email", "err");
      }
      $this->redirect('edit', $id);
    } 
  }
  
  public function actionPrintInvoice($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Invoice.phtml'); 
    }
    //$this->redirect('default');           
  }
  
  public function actionDeleteItem($id, $ordid) {
    if ($id > 0) {
      $orders = new OrdersModel();
      $ordItems = new OrdItemsModel(); 
      $ordItems->delete($id);
      $orders->recalcOrder($ordid);
      $this->flashMessage('Položka byla vymazána'); 
    }
    $this->redirect('edit', $ordid);           
  }
  
  /********************* facilities *********************/
  protected function createComponentOrderChangeStateForm() {
    $order = new OrdersModel(); 
    $form = new NAppForm();
    
    $form->addGroup('Změna stavu objednávky');
    $form->addSelect("ordstatus", "Nový stav objednávky", $order->getEnumOrdStatus());
    $form->addSubmit('newstate', 'Zmenit stav');
    $form->addSubmit('newstate_mail', 'Zmenit stav + poslat email');
    $form->onSuccess[] = array($this, 'orderChangeStateFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');    
    return $form;
  } 
  
  
  protected function createComponentOrderEditForm() {
    $order = new OrdersModel(); 
    $enums = new EnumcatsModel();
    $admins = new AdminsModel();
    $enumCountries = $enums->getEnumCountries(false);
    
    $form = new NAppForm();
    
    $form->addGroup('Základní údaje');        
    $form->addText("ordprice", "Celková cena:", 15)
      ->setDisabled(True);
    $form->addSelect("ordadmid", "Obchodník:", $admins->getEnumAdmins())
      ->setPrompt('');  
    $form->addText("ordparcode", "Číslo balíku:", 15);
    $form->addText("orddiscpercent", "Sleva v %:", 15)
      ->addRule(NForm::NUMERIC, "Sleva v procentech musí být číslo;");
      
    $form->addselect("orddelid", "Doprava:", $order->getEnumOrdDelId())
      ->addRule(NForm::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte');
    
    $form->addGroup('Fakturační adresa');
    
    $form->addText("ordiname", "Jméno:", 60);      
    $form->addText("ordilname", "Přijmení:", 60);      
    $form->addText("ordifirname", "Název firmy:", 60);      
        
    $form->addText("ordistreet", "Ulice:", 60);    
    $form->addText("ordistreetno", "Číslo popisné:", 60);    
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);  
    //$form->addSelect("ordicouid", "Země:", $enumCountries);
      
    $form->addText("ordic", "IČ:", 10);    
    $form->addText("orddic", "DIČ:", 10);
    
    $form->addGroup('Dodací adresa');        
    
    $form->addText("ordtel", "Telefon:", 10);    
    $form->addText("ordmail", "Email:", 20)
      ->addRule(NForm::FILLED, 'Prosím vyplňte email.');      
      
    $form->addText("ordstname", "Jméno:", 60);      
    $form->addText("ordstlname", "Přijmení:", 60);
    $form->addText("ordstfirname", "Název firmy:", 60);        
      
    $form->addText("ordststreet", "Ulice:", 60);  
    $form->addText("ordststreetno", "Číslo popisné:", 60);    
    $form->addText("ordstcity", "Město, obec:", 60);
    $form->addText("ordstpostcode", "PSČ:", 6);
    //$form->addSelect("ordstcouid", "Země:", $enumCountries);      
  
    $form->addGroup('Poznámka');
    
    $form->addTextArea("ordnote", "", 100, 3);
    /*
    $form->addGroup('Fakturační údaje');
    $form->addText("ordinvcode", "Číslo faktury:", 10);               
    //$form->addText("ordinvdate", "Datum vystavení:", 10);               
    */
    
    $form->addSubmit('makeorder', 'Uložit');
    $form->onSuccess[] = array($this, 'orderEditFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');    
    return $form;
  } 
  
  protected function createComponentOrdItemsEditForm() {
    $form = new NAppForm();
    $ordid = (int)$this->getParam("id");
    //nactu polozky objedmavky  
    if ($ordid > 0) {
      $form->addContainer('items');  
      $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordid, " ORDER BY oriname");
      foreach ($rows as $row) {
        $key = 'item_'.$row->oriid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("oriid", $row->oriid);
        $form["items"][$key]->addText("oriproid", "", 5)
          ->addRule(NForm::FILLED, 'Prosím vyplňte ID zboží.')
          ->setDefaultValue($row->oriproid);
        $form["items"][$key]->addText("oriname", "", 60)
          ->setAttribute('class', 'autocomplete')
          ->addRule(NForm::FILLED, 'Prosím vyplňte název položky.')
          ->setDefaultValue($row->oriname);
        $form["items"][$key]->addText("oriprice", "", 5)
          ->addRule(NForm::FILLED, 'Prosím vyplňte cenu položky.')
          ->setDefaultValue($row->oriprice);
        $form["items"][$key]->addText("oriqty", "", 3)
          ->addRule(NForm::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->oriqty);  
      }
      //postovne
      $deliv = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordid, " ORDER BY oriname");
      if ($deliv!==false) {
        $form['items']->addContainer('delivery');
        $form['items']['delivery']->addHidden("oriid", $deliv->oriid);
        $form['items']['delivery']->addHidden("oritypid", 1);
        $form['items']['delivery']->addHidden("oriqty", 1);
        $form['items']['delivery']->addText("oriname", "", 60)
          ->addRule(NForm::FILLED, 'Prosím vyplňte popis poštovného.')
          ->setDefaultValue($deliv->oriname);
        $form['items']['delivery']->addText("oriprice", "", 5)
          ->addRule(NForm::FILLED, 'Prosím vyplňte cenu poštovného.')
          ->setDefaultValue($deliv->oriprice);
      }   
      //nova polozka
      $form->addContainer('newitem');
      $form['newitem']->addHidden("oriordid", $ordid);
      $form['newitem']->addText("oriproid", "", 5);
      $form['newitem']->addText("oriname", "", 60)
        ->setAttribute('class', 'autocomplete');
      $form['newitem']->addText("oriprice", "", 5);
      $form['newitem']->addText("oriqty", "", 3)
        ->setDefaultValue(1);
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'ordItemsEditFormSubmitted');

    return $form;
  }
  
   public function ordItemsEditFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $ordid = (int)$this->getParam("id"); 
      $formVals = $form->getValues();
      $orders = new OrdersModel();
      $ordItems = new OrdItemsModel();
      foreach ($formVals['items'] as $item) {
        $id = $item["oriid"];
        unset($item["oriid"]);
        if (!empty($item["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $item["oriproid"]);
          $item["oriprocode"] = $product->procode;
          $item["oriprocode2"] = $product->procode2;
        }
        $ordItems->update($id, $item);
      }
      if (!empty($formVals['newitem']['oriname'])) {
        if (!empty($formVals['newitem']["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $formVals['newitem']["oriproid"]);
          $formVals['newitem']["oriprocode"] = $product->procode;
          $formVals['newitem']["oriprocode2"] = $product->procode2;
        }
        $ordItems->insert($formVals['newitem']);  
      }
      $orders->recalcOrder($ordid);

      $this->flashMessage("Položky objednávky byly aktualizovány, celková cena objednávky byla prepočítána.");
    } 
    $this->redirect('edit', $ordid);
  }
  
  public function searchFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) { 
        $this->sCode = Null;
        $this->sName = Null;
        $this->sAdmin = Null;
        $this->sStatus = Null;
        $this->sNotClosed = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else { 
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sAdmin = $vals["admin"];
        $this->sStatus = $vals["status"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }  
    }
    $this->redirect("Order:default");  
  }
  
  protected function createComponentSearchForm() {
    $orders = new OrdersModel();
    $catalogs = new CatalogsModel(); 
    $admins = new AdminsModel(); 
    
    $form = new NAppForm();  
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód objednávky", 10)
      ->setDefaultValue($this->sCode);  
    $form->addText("name", "Příjmení nebo název firmy", 10)
      ->setDefaultValue($this->sName);
    $form->addSelect("admin", "Obchodník", $admins->getEnumAdmins())
      ->setPrompt('')
      ->setDefaultValue($this->sAdmin);  
    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('')
      ->setDefaultValue($this->sStatus);
    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);
    
    $arr = array(
      'orddatec'=>'Data vytvoření',
    ); 
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);
    
    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    ); 
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);
                    
    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  } 
  
     
}