<?php

final class Admin_ManufacturerPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function editFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      
      $manufacturer = new ManufacturersModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
    
      //projdu formularova pole a ktere nejsou treba odstranim
      foreach ($vals as $key => $value) {
      }                                         

      try {
        if ($manufacturer->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('Manufacturer:default');        
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $manufacturer = new ManufacturersModel();
    $dataRows = dibi::query("SELECT * FROM manufacturers ORDER BY manname")
      ->fetchAssoc('manid');
    
    $this->template->dataRows = $dataRows;  
    //ciselnik statusu
    $this->template->enum_manstatus = $manufacturer->getEnumManStatus();
  }
  
  public function renderEdit($id) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $manufacturer = new ManufacturersModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $manufacturer->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id;   
    }
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');
    
    $manufacturer = new ManufacturersModel();
    
    $form = new NAppForm();
    
    $form->addText('manname', 'Název:', 30)
      ->addRule(NForm::FILLED, 'Název: je nutné vyplnit');

    $form->addText('manurl', 'URL:', 30);
    
    $form->addTextArea('mandesc', 'Popis:', 60, 10);
          
    $form->addSelect('manstatus', 'Status:', $manufacturer->getEnumManStatus());
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}