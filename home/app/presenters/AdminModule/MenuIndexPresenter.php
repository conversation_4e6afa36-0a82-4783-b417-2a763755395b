<?php

final class Admin_MenuIndexPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function menuEditFormSubmitted (NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      
      //pred ulozenim vymazu z cache prislusny ciselnik
      $cache = NEnvironment::getCache('enum');
      unset($cache['MenuIndexTree']);
      unset($cache['MenuIndexCombo']);
      
      $menus = new MenuIndexsModel();
      $id = (int)$this->getParam('id');
      
      //vezmu si obrazek pokud byl odeslan
      $image = null;
      if ($formVals["picture"]->isOk()) {
        $image = $formVals["picture"]->toImage();
      }
      unset($formVals["picture"]); 
      
      if ($id > 0) {
        $menus->update($id, $formVals);  
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $id = $menus->insert($formVals);  
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
      //upravim a ulozim obrazek
      $size = ($formVals["meibig"] ? $this->config["MEIPICSIZEBIG"] : $this->config["MEIPICSIZE"]);
      $this->saveImage($image, NEnvironment::getVariable('wwwDir')."/pic/menuindex/", "$id.jpg", array($size)); 
    }
    $this->redirect('default');
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $menus = new MenuIndexsModel();
    //$dataRows = $menus->fetchAll("SELECT * FROM menus WHERE ORDER BY menorder");
    $this->template->items = $menus->getEnumMenuIndexTree();           
    
    //ciselnik statusu
    $this->template->enum_menstatus = $menus->getEnumMenStatus();
  }
  
  public function renderEdit($id) {
    $form = new NAppForm();
    $form = $this['menuIndexEditForm'];
    
    if (!$form->isSubmitted()) {
      $menus = new MenuIndexsModel();
      if ($id > 0) {
        $dataRow = $menus->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
               
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;
        
        //zjistim jestli existuje obrazek
        $fileName = NEnvironment::getVariable('wwwDir')."/pic/menuindex/$id.jpg";
        if(file_exists($fileName)) $this->template->imagePath = "pic/menuindex/$id.jpg"; 
        
      } else {
        $defVals = array();
        $menmasid = $this->getParam("meimasid");
        if ($menmasid > 0) $defVals["meimasid"] = $menmasid;
        
        $form->setDefaults($defVals);
      }   
    }
  }
  
  public function renderDelete($id) {
    if ($id > 0) {
      //pred vymazanim vymazu z cache prislusny ciselnik
      $cache = NEnvironment::getCache('enum');
      unset($cache['MenuIndexTree']);
      unset($cache['MenuIndexCombo']);
      $menus = new MenuIndexsModel(); 
      $menus->delete($id);
      $this->flashMessage('Záznam byl vymazán'); 
    }
    $this->redirect('default');           
  }
  
  
  /********************* facilities *********************/

  protected function createComponentMenuIndexEditForm() {
    $menu = new MenuIndexsModel();
    $form = new NAppForm();
    $id = (int)$this->getParam('id');
    $type = $this->getParam('meisrctype');
    $form->addCheckbox('meibig', 'Velká ikonka');
    
    //pole pro seznam stranek
    $menpagarr = dibi::query("SELECT pagid, pagname FROM pages WHERE pagblock=0 ORDER BY pagname")
      ->fetchPairs('pagid', 'pagname');    
    $form->addSelect('meipagid', 'Textová stránka:', $menpagarr)
      ->setPrompt('');
    $catalogs = new CatalogsModel();
    $arr = $catalogs->getEnumCatalogCombo();
    unset($arr[0]);
    
    $form->addSelect('meicatid', 'Katalog:', $arr)
      ->setPrompt('');
    $form->addText('meiprocode', 'Kód zboží:', 15) ;
    $form->addText('meiurl', 'URL:', 100);
    
    //$form->addSelect('meimasid', 'Nadřízená úroveň:', $menu->getEnumMenuIndexCombo());
    $form->addHidden('meimasid', 0);     
    $form->addText('meiname', 'Název:', 15)
      ->addRule(NForm::FILLED, 'Prosím vyplňte název.');  
    
    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Obrázek bude zmenšen na rozměr '.$this->config["CATPICSIZE"].'. Zdrojový obrázek by měl mít stejný poměr stran. Nahráním nového obrázku ten původní přepíšete.') 
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');  
    
    $form->addText('meiorder', 'Pořadí:', 15)
      ->addRule(NForm::FILLED, 'Prosím vyplňte pořadí.');  
    
    $form->addSelect('meistatus', 'Status:', $menu->getEnumMenStatus())
      ->addRule(NForm::FILLED, 'Prosím vyplňte status.');
    
    $form->addSubmit('save', 'Uložit');  
    
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'menuEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  }
}