<?php

final class Admin_DiscountPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function editFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      
      $discount = new DiscountsModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
    
      try {
        if ($discount->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('default');        
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $discount = new DiscountsModel();
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM discounts ORDER BY disfrom");
    //ciselnik statusu
    $this->template->enum_disstatus = $discount->getEnumDisStatus();
  }
  
  public function renderEdit($id) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $discount = new DiscountsModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $discount->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id;   
    }
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');
    
    $discount = new DiscountsModel();
    
    $form = new NAppForm();
    
    $form->addText('disfrom', 'Cena od:', 30)
      ->addRule(NForm::FILLED, 'Cena od: je nutné vyplnit')
      ->addRule(NForm::INTEGER, "Cena od musí být celé číslo");

    $form->addText('disto', 'Cena do:', 30)
      ->addRule(NForm::FILLED, 'Cena do: je nutné vyplnit')
      ->addRule(NForm::INTEGER, "Cena do musí být celé číslo");
    
    $form->addText('dispercent', 'Sleva v %:', 30)
      ->addRule(NForm::FILLED, 'Sleva v procentech: je nutné vyplnit')
      ->addRule(NForm::NUMERIC, "Sleva v procentech musí být číslo");
            
    $form->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus());
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}