<?php

final class Admin_UserPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  /** @persistent */
  public $sName = '';
  
  /** @persistent */
  public $sMail = '';

  /** @persistent */
  public $sIc = '';
  
  /** @persistent */
  public $sFirName = '';
  
  public function userEditFormSubmitted (NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {
      $users = new UsersModel();
      $id = $this->getParam('id');
      if ($id > 0) {
        $users->update($id, $form->getValues());  
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $users->insert($form->getValues());  
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
    }
    $this->redirect('default');
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $users = new UsersModel();
    
    $where = "";
    if (!empty($this->sIc)) $where .= " usric like '$this->sIc%'";
    if (!empty($this->sName)) $where .= " (usrilname LIKE '%$this->sName%' OR  usrstlname LIKE '%$this->sName%')";
    if (!empty($this->sFirName)) $where .= " (usrifirname LIKE '%$this->sFirName%' OR  usrstfirname LIKE '%$this->sFirName%')";
    if (!empty($this->sMail)) $where .= " usrmail LIKE '%$this->sMail%'";
    if (!empty($where)) $where = " WHERE $where";    
    
    
    $dataSource = $users->getDataSource("SELECT * FROM users $where ORDER BY usrstname");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page; 
    $this->template->dataRows = $dataRows;         
    
    //ciselnik statusu
    $this->template->enum_usrstatus = $users->getEnumUsrStatus();
  }
  
  public function renderEdit($id) {
    $form = $this['userEditForm'];
    
    if (!$form->isSubmitted()) {
      $users = new UsersModel();
      $dataRow = $users->load($id);
      if (!$dataRow) {
        throw new NBadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($dataRow);
      
      $this->template->dataRow = $dataRow; 
    }
  }
  
  public function renderDelete($id) {
    if ($id > 0) {
      $users = new UsersModel(); 
      $users->delete($id);
      $this->flashMessage('Záznam byl vymazán'); 
    }
    $this->redirect('default');           
  }
  
  
  /********************* facilities *********************/

  protected function createComponentUserEditForm() {
    $user = new UsersModel();
    $form = new NAppForm();
    
    $form->addGroup('Změna emailu');
    
    $form->addText('usrmail', 'Email:', 30)
      ->setOption('description', ' slouží klientovi zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(NForm::FILLED, 'Prosím vyplňte email.')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::EMAIL, 'Email nemá správný formát');  
    
    $form->addText('usrdiscount', 'Sleva v %:', 30)
      ->addRule(NForm::FILLED, "Sleva v procentech musí být vyplněna")
      ->addRule(NForm::NUMERIC, "Sleva v procentech musí být číslo")      
      ->setDefaultValue(0);
    
    $form->addSelect('usrprccat', 'Cenová hladina', $user->getEnumUsrPrcCat());    
    
    $form->addTextArea('usrnote', 'Poznámka', 100, 5);    
    
    $form->addSelect('usrstatus', 'Status', $user->getEnumUsrStatus());    
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'userEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  }
  
  protected function createComponentSearchForm() {
    $orders = new OrdersModel();
    $catalogs = new CatalogsModel(); 
    
    $form = new NAppForm();  
    $form->addGroup("Vyhledávání");
    $form->addText("ic", "IČ", 10)
      ->setDefaultValue($this->sIc);
    $form->addText("stname", "Příjmení", 10)
      ->setDefaultValue($this->sName);
    $form->addText("firname", "Firma", 10)
      ->setDefaultValue($this->sFirName);              
    $form->addText("email", "Email", 10)
      ->setDefaultValue($this->sMail);
  
    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }
  
    public function searchFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) { 
        $this->sIc= Null;
        $this->sName = Null;
        $this->sFirName = Null;
        $this->sMail = Null;
      } else {
        $vals = $form->getValues();
        $this->sIc= $vals["ic"];
        $this->sName = $vals["stname"];
        $this->sFirName = $vals["firname"];
        $this->sMail = $vals["email"];
      }  
    }
    $this->redirect("User:default");  
  }
  
  public function actionClientCardPdf($id) {
    if ($id > 0) {
      $this->ClientCardPdf($id, 'D'); 
    }
    $this->terminate();
  }
  
  public function ClientCardPdf($id, $dest="D") {
    $template = $this->getTemplate();
    $users = new UsersModel();
    $template->dataRow = $users->load($id);
    $template->setFile(APP_DIR.'/../templates/pdf/clientCardPdf.phtml');
    $fname = ('klient-'.$template->dataRow->usrid.'-'.NStrings::webalize($template->dataRow->usriname.'-'.$template->dataRow->usrilname));
    // mPDF
    require(LIBS_DIR."/mpdf/mpdf.php");
    $mpdf = new mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P'); 
    $mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    $mpdf->SetAutoFont(0);
    $template->headers = (object) NULL;
    
    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    $mpdf->AddPage('P');
    $mpdf->WriteHTML($pdfHtml, 2);
    if ($dest=="I") {
      $name = NEnvironment::getVariable("tempDir")."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }  
    $mpdf->Output($name, $dest);
     
  }
  
  protected function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  } 
}