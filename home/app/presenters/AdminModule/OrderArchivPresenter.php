<?php

final class Admin_OrderArchivPresenter extends Admin_BasePresenter {
  public $backlink = '';
  
  public function renderDefault() {
    $baseModel = new OrdersArchivModel();
    $dataSource = $baseModel->getDataSource("SELECT oo.datum, oo.objednavka, oo.cenacelkem,oo.datexpedice, orr.jmeno, orr.prijmeni, orr.email FROM old_objednavka AS oo 
INNER JOIN old_registr orr ON (oo.uziv_id=orr.id) 
ORDER BY oo.datum DESC");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page; 
    $this->template->dataRows = $dataRows;            
  }
  
  public function renderDetail($id) {
    $order = dibi::fetch("SELECT * FROM old_objednavka where objednavka=%i",$id);
    if ($order) {
      $this->template->order = $order;           
      $this->template->ordItems = dibi::fetchAll("SELECT * FROM old_objednavka_polozky where cas=%i",$order->objednavka);           
      $this->template->user = dibi::fetch("SELECT * FROM old_registr where id=%i",$order->uziv_id);           
    } else {
      throw new NBadRequestException('Záznam nenalezen');
    }
  }
  
  protected function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  }  
}
