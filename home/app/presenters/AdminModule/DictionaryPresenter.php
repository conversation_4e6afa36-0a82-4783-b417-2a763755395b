<?php

final class Admin_DictionaryPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function editFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      $model = new DictionariesModel();
      if (empty($formVals["data"][0]["dicfrom"])) unset($formVals["data"][0]);
      foreach ($formVals["data"] as $key => $row) {     
        try {
          if ($key > 0) {
            //aktualizace
            $model->update($key, $row);
          } else {
            $id = $model->insert($row);
          }
        } catch (ModelException $e) {
          $form->addError($e->getMessage());
        }
      }
      $model->cacheClean();
      if (!$form->hasErrors()) $this->redirect('default');                                         
    }
  } 
  
  public function actionDelete($id) {
    $model = new DictionariesModel(); 
    $model->delete($id);
    $this->redirect('default');
  }
  
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM dictionaries ORDER BY dicfrom");
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    
    $model = new DictionariesModel();
    
    $form = new NAppForm();
    $rows = dibi::fetchAll("SELECT * FROM dictionaries ORDER BY dicfrom");
    $data = $form->addContainer('data');
    foreach ($rows as $row) {
      $con = $data->addContainer($row->dicid);      
      $con->addText('dicto_en', '', 100)
        ->setDefaultValue($row->dicto_en)
        ->addRule(NForm::FILLED, "Vyplňte překlad");
      $con->addText('dicnote', '', 50)
        ->setDefaultValue($row->dicnote);  
    }
    //nová polozka
    $con = $data->addContainer(0);
    $con->addText('dicfrom', '', 100);
    $con->addText('dicto_en', '', 100)
      ->addConditionOn($con["dicfrom"], NForm::FILLED)  
        ->addRule(NForm::FILLED, "Vyplňte překlad");
      $con->addText('dicnote', '', 50);  
        
        
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}