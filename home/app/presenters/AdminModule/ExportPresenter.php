<?php

final class Admin_ExportPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public $productFieldsDesc = array(
    "procode" => "Kód zboží", 
    "proname" => "Název zboží",
    "pronames" => "Zkrácený název zboží",
    "protype" => "Typ zboží (novinka, akce, ...)", 
    "promanufacturer" => "Výrobce",
    "proaccess" => "Dostupnost (počet dní)", 
    "prodescs" => "Krátký popis", 
    "prodesc" => "Dlouhý popis", 
    "propicname" => "název obrázku", 
    "propricecom" => "Cena běžná", 
    "propricea" => "Cena s DPH", 
    "propriceb" => "Cena B", 
    "propricec" => "Cena C", 
    "propriced" => "Cena D", 
    "provatname" => "Sazba DPH (základní, snížená)", 
    "procredit" => "Kredity", 
    "prorecycle" => "Recyklační poplatek",
    "prokeywords" => "Klíčová slova",
    "prowarranty" => "Záruka (slovy)",
    "proweight" => "Hmotnost (číslo bez jednotek)",
    "promascode" => "Kód nadřízeného zboží",
    "prostatus" => "Aktivní (0 - aktivní, 1 - blokovaný)",
    "proweight" => "Omezení dopravy (výčet ID dopravy oddělený čárkou)",
    "procatplaces" => "Zařazení do katalogu",
    "proparams" => "Parametry zboží",   
  );
  
  
  public function exportProductsSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {        
      $fields = "";
      $formVals = $form->getValues();
      foreach ($formVals as $key => $value) {
        if (substr($key, 0, 3)== "pro" && $value == true) {
          $fields .= "$key,";
        }
      }
      
      $fields = trim($fields, ',');
      $vals = array(
        'expname' => $formVals["expname"],
        'exptable' => 'products', 
        'expfields' => $fields,
      );
      
      $id = (int)$this->getParam('id'); 
      
      $export = new ExportsModel();
      
      if ($export->save($id, $vals)) {
        $this->flashMessage("Uloženo v pořádku");
        $this->redirect('default');
      }  
      
    /*
      //projdu polozky formu
      $vals = $form->getValues();
      $xls = '<table border="1">';
      
      //sestavim nadpisy sloupcu
      $xlstitle1 = "";
      $xlstitle2 = "";
      foreach ($vals as $field) {
        if (substr($field, 0, 3)== "pro") {
          $xlstitle1 .= "<td>".iconv('utf-8', 'cp1250', $this->productFieldsDesc[$field])."</td>\n";
          $xlstitle2 .= "<td>".$field."</td>\n";
        }
      }    
      $xls .= "
<tr>$xlstitle1</tr>
<tr>$xlstitle2</tr>";
      //sestavim SQL
      $sqlfields = "";
      foreach ($vals as $field => $desc) {
        if (substr($field, 0, 3)== "pro") {
          
          switch ($field) {
             case 'proparams':
             case 'procatplaces':
             case 'promascode':
             case 'protype':
             case 'promanufacturer':
             case 'provatname':
               $sqlfields .= "'' AS $field,";
               break;
             default:
               $sqlfields .= "$field,";
               break;
          }
        }
      }
      $sqlfields = trim($sqlfields, ',');
      $product = new ProductsModel();
      //$product
      $rows = dibi::fetchAll("SELECT $sqlfields FROM products");
      //$rows = $rs->fetchAssoc();
      foreach ($rows as $row) {
        $xls .= '
<tr>';
        foreach ($row as $key => $value) {
          $xls .= $this->getXLSCell($key, $value);
        }
        $xls .= '<tr>';  
      }
      $xls .= '
</table>';
      header ("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
      header ("Last-Modified: " . gmdate("D,d M YH:i:s") . " GMT");
      header ("Cache-Control: no-cache, must-revalidate");
      header ("Pragma: no-cache");
      header ("Content-type: application/x-msexcel");
      header ("Content-Disposition: attachment; filename=\"export_zbozi.xls\"" );
      header ("Content-Description: PHP/INTERBASE Generated Data" );
      echo $xls;
      
      */     
    }
    
  }
  
  public function actionExportXML($expid) {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="crosstab.xml"');
    header('Cache-Control: max-age=0');
    $this->setLayout('empty');
    $this->setView('exportXML');
    $this->template->cols=$cols;
    $this->template->data=$data;
  } 
  
  public function actionDelete($expid) {
    $export = new ExportsModel();
    $export->delete($expid);
    $this->flashMessage("Položky vymazána");
    
    $this->redirect('default');
  }
  
  /********************* view default *********************/ 
  
  public function renderEdit($id) {
    $form = $this['exportProductsForm'];
    if (!$form->isSubmitted()) {
      $export = new ExportsModel();
      $fields = array();
      if ($id > 0) {
        $dataRow = $export->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        
        $export = new ExportsModel();
        $dataRow = $export->load($id);
        $fields = array_flip(explode(",", $dataRow->expfields));
        
        $form->setDefaults($dataRow);
        $form->setDefaults($fields);
        
        $this->template->dataRow = $dataRow; 
      } else {
        $fields = $this->productFieldsDesc;
      }
      foreach ($fields as $key => $value) {
        $fields[$key] = true;
      }
      $form->setDefaults($fields);
      
      $this->template->id = $id;   
    }  
  }
  
  public function renderDefault() {
    $export = new ExportsModel(); 
    $this->template->dataRows = dibi::fetchAll("Select * FROM exports");
    
  }
  
  /********************* facilities *********************/

  protected function createComponentExportProductsForm() {
  
    $form = new NAppForm();
    
    $form->addText('expname', "Název exportu", 100)
      ->addRule(NForm::FILLED, "vyplňte název exportu");
    
    $id = (int)$this->getParam('id');
      
    foreach ($this->productFieldsDesc as $key => $value) {
      if ($key == 'procode') {
        $form->addHidden($key, true); 
      } else {
        $form->addCheckbox($key, $value);
      }  
    } 
    
    $form->addSubmit('save', 'Uložit export')->getControlPrototype()->class('default');    
    $form->onSuccess[] = array($this, 'exportProductsSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}