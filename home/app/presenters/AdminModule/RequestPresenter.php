<?php

final class Admin_RequestPresenter extends Admin_BasePresenter {

  public function renderDefault() {
    $baseModel = new OrdersArchivModel();
    $reqs = new RequestsModel();

    $dataSource = $baseModel->getDataSource("
      SELECT * FROM requests 
        INNER JOIN products ON (proid=reqproid)
        LEFT JOIN admins ON (admid=reqadmid)
        group by reqid
        order by reqid DESC   
      ");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    $this->template->enum_reqstatus = $reqs->getEnumReqStatus();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $reqs = new RequestsModel();
      $pros = new ProductsModel();
      $dataRow = $reqs->load($id);
      if (!$dataRow) {
        throw new NBadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;
      if (!empty($dataRow->reqproid)) {
        $this->template->product = $pros->load($dataRow->reqproid);
      }
    }

    $this->template->statusLog = dibi::fetchAll("SELECT * FROM requests_log WHERE relreqid=%i", $id);
    $this->template->enum_reqstatus = $reqs->getEnumReqStatus();
  }

  protected function createComponentEditForm() {
    $ords = new OrdersModel();
    $reqs = new RequestsModel();
    $adms = new AdminsModel();

    $form = new NAppForm();

    $form->addselect("reqstatus", "Status:", $reqs->getEnumReqStatus())
      ->setPrompt('Vyberte');

    $form->addSelect('reqadmid', 'Správce poptávky', $adms->getEnumAdmins())
      ->setPrompt("");

    $form->addText('reqfirname', 'Firma', 50);
    $form->addText('reqname', 'Jméno a příjmení', 50)
      ->addRule(NForm::FILLED, 'Prosím vyplňte jméno a příjmení.');

    $form->addText('reqmail', 'Váš email')
      ->addRule(NForm::FILLED, 'Prosím vyplňte email.')
      ->addRule(NForm::EMAIL, 'Email nemá správný formát');

    $form->addText('reqphone', 'Telefonní číslo')
      ->addRule(NForm::FILLED, 'Prosím vyplňte telefonní číslo.');

    $form->addTextArea('reqnote', 'Poznámka', 100, 10);

    $form->addTextArea('reqnoteint', 'Interní poznámka', 100, 10);

    $form->addselect("reqdelid", "Doprava:", $ords->getEnumOrdDelId())
      ->setPrompt('Vyberte');



    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    $form->addSubmit('save_order', 'Uložit a vytvořit objednávku')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $reqs = new RequestsModel();
      $id = $this->getParam('id');
      if ($id > 0) {
        $reqs->update($id, $form->getValues());
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $id = $reqs->insert($form->getValues());
        $this->flashMessage('Nový záznam uložen v pořádku');
      }

      if ($form['save']->isSubmittedBy()) {
        $this->redirect('edit', $id);
      }
      if ($form['save_order']->isSubmittedBy()) {
        $req = $reqs->load($id);
        if (empty($req->reqdelid)) {
          $this->flashMessage("Je nutné zadat dopravu.", "err");
          $this->redirect('edit', $id);
        }

        $ords = new OrdersModel();
        $ordId = $ords->createFromRequest($id);
        $reqs->update($id, array("reqstatus" => 2));
        $this->redirect('Order:edit', $ordId);
      }
    }

    $this->redirect('default');
  }
  
  protected function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  }  
}
