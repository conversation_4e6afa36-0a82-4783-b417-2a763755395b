<?php

final class Admin_CatalogPresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function catalogEditFormSubmitted (NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {      
      $catalogs = new CatalogsModel();
      $id = (int)$this->getParam('id');
      $formVals = $form->getValues();
      
      //kontrola zda nedal stejnou nadrizenou uroven jako je id
      if ($id > 0 && $id == $formVals["catmasid"]) {
        $form->addError("Nelze zadat do nadřízené kategorie editovanou kategorii.");
      } else {
        //vezmu si obrazek pokud byl odeslan
        $image = null;
        if ($formVals["picture"]->isOk()) {
          $image = $formVals["picture"]->toImage();
        }
        unset($formVals["picture"]); 
        if ($id > 0) {
          $catalogs->update($id, $formVals);  
          $this->flashMessage('Aktualizováno v pořádku');
          
          //updatnu keymaster v prislisnem katalogu a produktu pokud je to korenova kategorie
          if ((int)$formVals["catmasid"] == 0) {
            $keymaster = (empty($formVals["catkey"]) ? NStrings::webalize($formVals["catname"]) : $formVals["catkey"]) ;
            //produkty:
            dibi::query("UPDATE products SET prokeymaster=%s", $keymaster, " WHERE promasid=%i", $id);
            //katalog
            //neni treba resit protoze se pregeneruje cele znovu po ulozeni
            //dibi::query("UPDATE catalogs SET catkeymaster=%s", $keymaster, " WHERE catpathids LIKE '|$id|%' AND catmasid>0");
          } 
          
        } else {
          $id = $catalogs->insert($formVals);  
          $this->flashMessage('Nový záznam uložen v pořádku');
        }
        //upravim a ulozim obrazek
        $this->saveImage($image, NEnvironment::getVariable('wwwDir')."/pic/catalog", "$id.jpg", array($this->config["CATPICSIZE"])); 
        
        //vycistim cache
        $cache = NEnvironment::getCache('enum');
        unset($cache['CatalogTree']);
        unset($cache['CatalogCombo1']);
        unset($cache['CatalogCombo0']);
        //naplneni 2 urovni katalogu do menu
        unset($cache['CatalogMenu']); 
      }  
      if (!$form->hasErrors()) $this->redirect('default');
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $catalogs = new CatalogsModel();
    //$dataRows = $catalogs->fetchAll("SELECT * FROM catalogs WHERE ORDER BY catorder");
    $this->template->items = $catalogs->getEnumCatalogTree();           
    
    //ciselnik statusu
    $this->template->enum_catstatus = $catalogs->getEnumCatStatus();
  }
  
  public function renderEdit($id) {
    $form = new NAppForm();
    $form = $this['catalogEditForm'];
    
    if (!$form->isSubmitted()) {
      $catalogs = new CatalogsModel();
      if ($id > 0) {
        $dataRow = $catalogs->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow;
        
        //zjistim jestli existuje obrazek
        $fileName = NEnvironment::getVariable('wwwDir')."/pic/catalog/$id.jpg";
        if(file_exists($fileName)) $this->template->imagePath = "pic/catalog/$id.jpg"; 
        
      } else {
        $defVals = array();
        $catmasid = $this->getParam("catmasid");
        if ($catmasid > 0) $defVals["catmasid"] = $catmasid;
        
        $form->setDefaults($defVals);
      }   
    }
  }
  
  public function renderDelete($id) {
    if ($id > 0) {
      //zjistim jestli nema podrizene vetve
      $cnt = dibi::fetchSingle("SELECT count(*) FROM catalogs WHERE catmasid=%i", $id);
      if ($cnt == 0) {
        $catalogs = new CatalogsModel(); 
        $catalogs->delete($id);
        $this->flashMessage('Záznam byl vymazán.');
        $cache = NEnvironment::getCache('enum');
        unset($cache['CatalogTree']);
        unset($cache['CatalogCombo1']);
        unset($cache['CatalogCombo0']);
        //naplneni 2 urovni katalogu do menu
        unset($cache['CatalogMenu']); 
      } else {
        $this->flashMessage('Záznam nebyl vymazán. Existují jemu podřízené větve katalogu.', 'err'); 
      }  
    }
    $this->redirect('default');           
  }

  public function actionDeleteImage($catId) {
    $filename = $catId . ".jpg";
    @unlink(WWW_DIR . '/pic/catalog/' . $filename);
    $this->flashMessage("Obrázek byl vymazaný");
    $this->redirect('edit', $catId);
  }

  
  /********************* facilities *********************/

  protected function createComponentCatalogEditForm() {
    $catalog = new CatalogsModel();
    $form = new NAppForm();
    $id = (int)$this->getParam('id');
    
    $form->addSelect('catmasid', 'Nadřízená úroveň:', $catalog->getEnumCatalogCombo())
      ->addRule(NForm::FILLED, 'Prosím vyplňte název.');
    
    $form->addText('catname', 'Název:', 30)
      ->addRule(NForm::FILLED, 'Prosím vyplňte název.');
      
    $form->addText('catkey', 'URL klíč:', 30)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu kategorie');  
    
    $form->addText('catkeywords', 'Klíčové slova:', 30)
      ->setOption('description', 'slova oddělené čárkou');
      
    $form->addText('catgooglecat', 'Katalog google:', 100);  
    
    $form->addTextArea('catdescription', 'Description:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', NHtml::el('p')->class('charsRemaining'));
      
    $form->addTextArea('catdesc', 'Popis:', 75, 5);
      $form['catdesc']->getControlPrototype()->class('mceEditor'); 
    
    
    
    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Obrázek bude zmenšen na rozměr '.$this->config["CATPICSIZE"].'. Zdrojový obrázek by měl mít stejný poměr stran. Nahráním nového obrázku ten původní přepíšete.') 
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');  
    
    $form->addText('catorder', 'Pořadí:', 15)
      ->addRule(NForm::FILLED, 'Prosím vyplňte pořadí.');  
    
    $form->addSelect('catstatus', 'Status:', $catalog->getEnumCatStatus())
      ->addRule(NForm::FILLED, 'Prosím vyplňte status.');
        
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');  
    
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'catalogEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}