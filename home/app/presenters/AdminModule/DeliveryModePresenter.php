<?php

final class Admin_DeliveryModePresenter extends Admin_BasePresenter {

  public $backlink = '';
  
  public function editFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      
      $deliverymode = new DeliveryModesModel();
      $id = (int)$this->getParam('id');
      $vals = $form->getValues(); 
    
      //projdu formularova pole a ktere nejsou treba odstranim
      foreach ($vals as $key => $value) {
      }                                         

      try {
        if ($deliverymode->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('DeliveryMode:edit', $id);        
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault($status=0) {
    $deliverymode = new DeliveryModesModel();
    $statusSql = ($status == 1 ? ''  : ' AND delstatus=0');
    $dataRows = dibi::query("SELECT * FROM deliverymodes WHERE delmasid=0 $statusSql ORDER BY delorder")
      ->fetchAssoc('delid');
    
    foreach ($dataRows as $key => $value) {
      $dataRows[$key]["subItems"] = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delmasid=$key $statusSql ORDER BY delorder");
    }
    $this->template->dataRows = $dataRows;  
    //ciselnik statusu
    $this->template->enum_delstatus = $deliverymode->getEnumDelStatus();
  }
  
  public function renderEdit($id, $masid=0) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $deliverymode = new DeliveryModesModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $deliverymode->load($id);
        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      if ($masid > 0) $this->template->delMas = $deliverymode->load($masid);  
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id;   
    }
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParam('id');
    $masid = (int)$this->getParam('masid');
    
    $deliverymode = new DeliveryModesModel();
    if ($id > 0) {
      $dataRow = $deliverymode->load($id);
      $masid = $dataRow->delmasid;
    }
    
    $form = new NAppForm();
    
    $form->addHidden('delmasid', $masid);
    
    $form->addText('delname', 'Název:', 30)
      ->addRule(NForm::FILLED, 'Název: je nutné vyplnit');
    
    
    if ($masid > 0) {
      $form->addSelect('delcode', 'Typ platby:', $deliverymode->getEnumPayTypes())
        ->setPrompt("");
    
     /*
      $enums = new EnumcatsModel();
      $form->addSelect("delcouid", "Region cílové země", $enums->getEnumDelRegions())
        ->setPrompt("vyberte ...");
     */
     
      $form->addNumeric('dellimitfrom', 'Doprava zdarma: limit od [Kč]:', 30, 10)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::FLOAT, "Doprava zdarma: limit od: hodnota musí být číslo");
      $form->addNumeric('dellimitto', 'Doprava zdarma: limit do [Kč]:', 30, 10)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::FLOAT, "Doprava zdarma: limit do: hodnota musí být číslo");
      $form->addNumeric('delprice', 'Cena:', 30, 5)
        ->addRule(NForm::FILLED, "Cena: hodnota musí být vyplněna")
        ->addRule(NForm::FLOAT, "Cena: hodnota musí být číslo");
        
      $form->addText('delurlparcel', 'URL sledování zásilky:', 130);
    
      $form->addTextArea('deldesc', 'Popis:', 100, 5);
    } else {
      $form->addNumeric('delweightlimitfrom', 'Hmotnost od:', 30, 10)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::FLOAT, "Hmotnost od: hodnota musí být číslo");
      $form->addNumeric('delweightlimitto', 'Hmotnost do:', 30, 10)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::FLOAT, "Hmotnost do: hodnota musí být číslo");    
          
      $form->addCheckbox('delspecdel', 'individuální doprava');    
    }
    
    $form->addText('delorder', 'Pořadí:', 30, 10)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::INTEGER, "Pořadí: hodnota musí být celé číslo");
          
    $form->addSelect('delstatus', 'Status:', $deliverymode->getEnumDelStatus());
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}