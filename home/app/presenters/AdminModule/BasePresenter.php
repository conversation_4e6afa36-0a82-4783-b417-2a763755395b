<?php

abstract class Admin_BasePresenter extends BasePresenter {
  const LOGIN_NAMESPACE = 'admin';
  /** identita */
  protected $user;
  protected $adminData = Null;
  
  protected function startup() {    
    parent::startup();
     
    // autorizace administratora
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);
    
    if ($this->user->isLoggedIn() && $this->action == "login") {
      $this->redirect('Admin:default');
    } else {  
      if ($this->action != "login") { 
        if (!$this->user->isLoggedIn()) {
          if ($this->user->getLogoutReason() === IUserStorage::INACTIVITY) {
            $this->flashMessage('Byl/a jste odhl<PERSON>šen/a z důvodu delší neaktivity.');
          }
          $backlink = $this->getApplication()->storeRequest();
          $this->redirect('Admin:login', $backlink);
        }
      }
    }
    $this->adminData = false;
    if ($this->user->isLoggedIn()) {
      $admins = new AdminsModel();
      $this->adminData = $admins->load($this->user->id);
    }  
      
    if (!$this->adminData) {
      $this->user->logout();
      $this->adminData = new DibiRow(array('admid'=>0));
    }   
  }
  
  protected function beforeRender() {
    //nactu administratora
    $this->template->identity = $this->user; 
    $this->template->admin = $this->adminData;
    
    //nactu nazev DB ripojeny
    $config = dibi::getConnection('live')->getConfig();
    $this->template->dbName  = $config["database"];
     
    parent::beforeRender();
  }
  
  protected function saveImage($image, $path, $filename, $sizes) {
    //upravim a ulozim obrazek
    if (isset($image) && is_array($sizes)) {
      foreach ($sizes as $size) {
        $img = clone $image;
        $arr = explode("x", $size);
        $w = 0;
        $h = 0;
        $dir = '';
        if (isset($arr[0])) $w = (int)$arr[0];
        if (isset($arr[1])) $h = (int)$arr[1];
        if (isset($arr[2])) $dir = $arr[2];
  
        if ($w > 0 || $h > 0) {    
          $img->resize($w, $h); // resize, který prostor vyplní a možná překročí
            //->crop('50%', '50%', $w, $h); // ořezání po stranách
          $blank = NImage::fromBlank($w, $h, NImage::rgb(255, 255, 255));
          $blank->place($img, '50%', '50%');  
          
          $watermarkFile = $path.($dir!="" ? '/'.$dir : '').'/watermark.png';
          if (file_exists($watermarkFile)) {
            $watermark = NImage::fromFile($watermarkFile);
            $blank->place($watermark, 0, 0);
          }  
          
          //$img->sharpen();
          $blank->save($path.($dir!="" ? '/'.$dir : '').'/'.$filename, 100, NImage::JPEG);
        }  
      }        
    }
  }
}