<?php

final class Admin_AdminPresenter extends Admin_BasePresenter {
  
  /** @persistent */
  public $backlink = '';
 
  public function actionLogin($backlink) {

  }
  
  public function actionLogout() {
    $this->user->logout();
    $this->flashMessage("Odhlášení proběhlo úspěšně.");
    $this->redirect('Admin:login');
  }  

  public function loginFormSubmitted($form) {
    try {
      $this->user->login($form['admmail']->getValue(), $form['admpassw']->getValue(), self::LOGIN_NAMESPACE);
      $this->getApplication()->restoreRequest($this->backlink);
      $this->redirect('Admin:default');

    } catch (NAuthenticationException $e) {
      $form->addError($e->getMessage());
    }
  }
  
  
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    //seznam aktualnich objednavek
    $orders = new OrdersModel();
    $dataRows = dibi::fetchAll("
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, d.delprice AS delprice, admname,
    (SELECT orldatec FROM orders_log WHERE orlordid=ordid AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1) AS datesend,
    (SELECT SUM(oriprobigsize) FROM orditems WHERE oriordid=ordid) AS oriprobigsize,
    (SELECT SUM(oriprooffer) FROM orditems WHERE oriordid=ordid) AS oriprooffer
    FROM orders 
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid) 
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    WHERE ordstatus IN (0,1,2,3,6) 
    ORDER BY orddatec DESC");
    $this->template->dataRows = $dataRows;           
  
    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }
  
  /********************* view add, edit *********************/ 
  
  public function renderEditSelf() {
    $this->renderEdit($this->user->id);
  }
    
  public function renderEdit($id) {
    if ($this->adminData->admrole!='superadmin' && $this->adminData->admid != $id) {
      $this->flashMessage("Nemáte oprávnění editovat cizí účty a vytvářet nové.", 'err');
      $this->redirect('list');
    }  
    
    $form = $this['adminForm'];
    
    if (!$form->isSubmitted() && $id > 0) {
      $admin = new AdminsModel();
      $row = $admin->load($id);
      if (!$row) {
        throw new NBadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($row);
    }
  }
  
  public function renderList() {
    $admin = new AdminsModel();
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM admins ORDER BY admname");
    $this->template->enum_admstatus = $admin->getEnumAdmStatus();
  }
  
  public function adminFormSubmitted(NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVars = $form->getValues();
      
      $id = (int) $this->getParam('id');
      $admin = new AdminsModel();
      
      if ($id > 0) {
        $admRow = $admin->load($id);
        //kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if ($formVars["admpassw_old"] != "") {
          if (md5($formVars["admpassw_old"]) != $admRow["admpassw"]) {
            $this->flashMessage("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno");
            unset($formVars["admpassw"]);
          } else {
            $formVars["admpassw"] = md5($formVars["admpassw"]);  
            $passw_changed = true;
          }
        } else {
          unset($formVars["admpassw"]);
        }  
      } else {
        $formVars["admpassw"] = md5($formVars["admpassw"]);  
      }
      
      unset($formVars["admpassw_old"]);
      unset($formVars["admpassw2"]);
      
      if ($id > 0) {
        $admin->update($id, $formVars);
        $this->flashMessage('Údaje byly aktualizovány.');
      } else {
        $id = $admin->insert($formVars);
        $this->flashMessage('Účet byl vytvořen.');
      }
    }
    $this->redirect('edit', $id);
  }
  
  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentAdminLoginForm() {
    $form = new NAppForm;
    $form->addText('admmail', 'Přihlašovací jméno:')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');

    $form->addPassword('admpassw', 'Heslo:')
      ->addRule(NForm::FILLED, 'Prosím vyplňte heslo.');

    $form->addSubmit('login', 'Přihlásit se');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
  protected function createComponentAdminForm() {
    $id = $this->getParam('id');
        
    $admin = new AdminsModel();
    $form = new NAppForm;
    $form->addText('admname', 'Jméno:', 40)
      ->addRule(NForm::FILLED, 'Prosím vyplňte Jméno.');
 
    $form->addText('admmail', 'Login:', 40)
      ->addRule(NForm::FILLED, 'Prosím vyplňte Login.');
    if (empty($id)) {
      $form->addPassword('admpassw', 'Heslo:')
        ->addRule(NForm::FILLED, 'Prosím vyplňte heslo.');      
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addRule(NForm::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(NForm::FILLED) 
          ->addRule(NForm::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    } else if ($this->adminData->admrole=='superadmin' || $id == $this->adminData->admid) {
      $form->addPassword('admpassw_old', 'Původní heslo:');      
      $form->addPassword('admpassw', 'Nové heslo:')
        ->addConditionOn($form["admpassw_old"], NForm::FILLED)
          ->addRule(NForm::FILLED, 'Prosím vyplňte nové heslo.');      
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addConditionOn($form["admpassw_old"], NForm::FILLED)
          ->addRule(NForm::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(NForm::FILLED) 
          ->addRule(NForm::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    }
    
    
    
    if ($this->adminData->admrole=='superadmin') {
      $form->addSelect('admrole', 'Role', $admin->getEnumAdmRole());      
      $form->addSelect('admstatus', 'Status', $admin->getEnumAdmStatus());
    }
    
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = callback($this, 'adminFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
}