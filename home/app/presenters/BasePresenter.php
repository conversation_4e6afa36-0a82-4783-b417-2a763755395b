<?php

abstract class BasePresenter extends NPresenter {
  
  /** nastaveni serveru */ 
  public $config = array();
    /** prekladac */
  protected $translator;
  
  public $lang = 'cs';
  
  protected function beforeRender() {
    //nactu nastaveni
    $this->template->configApp = NEnvironment::getConfig('app'); 
    $this->template->config = $this->config;
    
    //predzvykam velikosti obrazku
    $size = explode('x', $this->config["PROPICSIZE_LIST"]);
    $this->template->picListWidth = $size[0];
    $this->template->picListHeight = $size[1];
    
    $size = explode('x', $this->config["PROPICSIZE_DETAIL"]);
    $this->template->picDetailWidth = $size[0];
    $this->template->picDetailHeight = $size[1];
    
    $size = explode('x', $this->config["PROPICSIZE_BIG"]);
    $this->template->picBigWidth = $size[0];
    $this->template->picBigHeight = $size[1];
    
    $this->template->setTranslator($this->translator);
    $this->template->lang = $this->lang;
  }
  
  protected function startup() {
    parent::startup(); 
    //nactu nastaveni z datatabaze
    
    //nactu uzivatelske nastaveni do cache
    $cache = NEnvironment::getCache('app');
    if (!isset($cache['config'])) {
      //naplnim uzivatelske nastaveni do cache
      $result = dibi::query('SELECT * FROM config');
      $arr = $result->fetchPairs('cfgcode', 'cfgvalue');   
      $cache->save('config', $arr, array(
        'expire' => time() + 60 * 10,
        'refresh' => False,
      ));
    }
    $this->config = $cache['config'];
    //nastaveni prekladani
    $this->lang = $this->config["SERVER_LANGUAGE"];
    $dic = array();
    If ($this->lang == 'en') {
      //nactu slovnik  
      //pokud neni cache vytvorim - mela by byt vzdy vytvorena
      $dic = DictionariesModel::getDictionary($this->lang);  
    }
    $this->translator = new MyTranslator($this->lang, $dic);
  }
  
  public function printOrder($id, $dest="I", $templateName='Order.phtml') {
    $orders = new OrdersModel();
    $enums = new EnumcatsModel();
    $template = $this->getTemplate();
    $template->order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    $template->ordItems = dibi::fetchAll("SELECT * FROM orditems LEFT JOIN products ON (oriproid=proid) WHERE oriordid=%i", $id, "  ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
    $template->ordItemDelivery = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $id);
    $template->payDate = dibi::fetchSingle("SELECT ordinvdate + INTERVAL 14 DAY FROM orders WHERE ordid=%i", $id);
    $template->enum_delmode = $orders->getEnumOrdDelIdSimple();
    $template->enum_countries = $enums->getEnumCountries();
    $template->lang = $this->lang;
    $template->setTranslator($this->translator);
    $template->setFile(APP_DIR.'/../templates/pdf/'.$templateName);
    $fname = ($templateName=='Order.phtml' ? 'objednavka-'.$template->order->ordcode : 'faktura-'.$template->order->ordinvcode);
    // mPDF
    require(LIBS_DIR."/mpdf/mpdf.php");
    $mpdf = new mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P'); 
    $mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    $mpdf->SetAutoFont(0);
    $template->headers = (object) NULL;
    $pdfHtml = (string) $template; // vygenerujeme šablonu už nyní
    $mpdf->AddPage('P');
    $mpdf->WriteHTML($pdfHtml, 2);
    if ($dest=="I") {
      $name = NEnvironment::getVariable("tempDir")."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }  
    $mpdf->Output($name, $dest); 
  }
  
  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }
  
  protected function mailSend($mailTo, $subject, $bodyTemplate, $mailFrom="") {
    $bodyTemplate->setTranslator($this->translator);
    $bodyTemplate->lang = $this->lang;
    $mail = new NMail();
    if ($mailFrom == "") $mailFrom = $this->config["SERVER_MAIL"];
    $mail->setFrom($this->config["SERVER_NAMESHORT"].' <'.$mailFrom.'>');
    
    $mail->addTo($mailTo);
    $mail->setSubject($subject." - ".$this->config["SERVER_NAMESHORT"]);
    $mail->setHtmlBody($bodyTemplate);
    $mail->send(); 
  }
  
  /**
   * Formats view template file names.
   * @return array
   */
  public function formatTemplateFiles() {
    $root = APP_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';

    return array(
      "$root/$module/$presenter.$this->view.phtml",
    );
  }
  
  public function getPriceVat($price, $vatid) {
    $vatLevel = (int)$this->config["VATTYPE_".$vatid];
    return($price * (1+($vatLevel / 100)));
  }
  
  /**
   * Formats layout template file names.
   * @return array
   */
  public function formatLayoutTemplateFiles() {
 
    $root = APP_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';
    $layout = $this->layout ? $this->layout : 'layout';    
    return array(
      "$root/$module/@$layout.phtml",
      "$root/@$layout.phtml",
    );
  }
}