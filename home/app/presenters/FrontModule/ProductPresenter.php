<?php

final class Front_ProductPresenter extends Front_BasePresenter {
  
  public function renderDetailOld($id, $key) {
    
    
  }
  
  public function renderDetail($id, $key1="", $key2="") {
    $products = new ProductsModel();
    $products->setPrcCat($this->userData->usrprccat);
    //aktualni polozka katalogu
    $product = $products->load($id);
    if ($product === false) {
      throw new NBadRequestException('Položka nenalezena', '404');
    } else if ($product->prostatus == 1) {
      $this->flashMessage("Tato položka byla vyřazena z nabídky.", "err");
      throw new NBadRequestException('Položka nenalezena', '404');
    } else {
      //kontrola platnosti URL
      $key1Data = $product->prokeymaster;
      $key2Data = (!empty($product->prokey) ? $product->prokey : NStrings::webalize($product->proname));
      
      //pokud se zmenil klic presmeruju na novy
      if ($key1 != $key1Data || $key2 != $key2Data) $this->redirect(301, 'Product:detail', array('id'=>$id, 'key1'=>$key1Data, 'key2'=>$key2Data));
      $this->template->product = $product; 
    }
    
    
    /*
    $this->template->catalogs = dibi::fetchAll("SELECT catid, catmasid, catkey, catpathids, catpath, catname FROM catalogs INNER JOIN catplaces ON (catid=capcatid) WHERE capproid=%i", $id);
    
    if (isset($this->template->catalogs[0])) {
      //id aktualni kategorie
      $this->template->thisCatId = $this->template->catalogs[0]->catid;
      $this->template->thisCatMasId = $this->template->catalogs[0]->catmasid;
    }
    
    //naplnim korenove urovne kde je zbozi zarazeno
    $this->template->catalogsMas = dibi::query("SELECT catid, catkey, catpathids, catpath, catname FROM catalogs INNER JOIN catplaces ON (catid=capcatid) WHERE catmasid=0 AND capproid=%i", $id)
                                      ->fetchAssoc('catid');
    */
    //naplnim katalogy ve kterych je zbozi zarazeno
    $ses = NEnvironment::getSession('catlog');
    $lastCatId = (int)$ses->lastCatId;
    
    //zjistim zda je zbozi v teto kategorii zarazeno
    $cnt = dibi::fetchSingle("SELECT COUNT(*) FROM catplaces WHERE capcatid=%i", $lastCatId, " AND capproid=%i", $product->proid);
    if ($cnt > 0) { 
      $this->template->thisCatId = $lastCatId;
      $catalogData = dibi::fetch("SELECT * FROM catalogs WHERE catid=%i", $lastCatId);
    } else {
      $catalogData = dibi::fetch("SELECT * FROM catalogs INNER JOIN catplaces ON (catid=capcatid) WHERE capproid=%i", $product->proid, "ORDER BY catorder LIMIT 1");
    }
    if ($catalogData) {  
      $idPath = explode('|', trim($catalogData->catpathids, '|'));
      $menuCatalogL = array();
      foreach ($idPath as $catid) {
        $menuCatalogL[$catid] = dibi::fetchAll("SELECT * from catalogs WHERE catstatus=0 AND catmasid=$catid ORDER BY catorder");
        if ($catalogData->catid == $catid) break;  
      }

      $this->template->menuCatalogL = $menuCatalogL;
      $this->template->catalogData = $catalogData;
    }
    
    //naplnim vyrobce
    $this->template->manufacturer = dibi::fetch("SELECT manid, manname FROM manufacturers WHERE manid=$product->promanid");
    
    //naplnim typ
    $this->template->enum_protype = $products->getEnumProTypId();
  
    //naplnim parametry zbozi
    $this->template->proParams = dibi::fetchAll("SELECT prpname, prpvalue FROM proparams WHERE prpproid=%i", $id, " ORDER BY prpid");
    
    //naplnim prilohy
    $this->template->files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $id);
    
    //naplnim obrazky
    $images = array();
    for($i=1;$i<=10;$i++){
      $fName = ($product->propicname != "" ? $product->propicname : $product->procode)."_$i".'.jpg';
      $picPath = WWW_DIR."/pic/product/list/";
      if (file_exists($picPath.$fName)) {
        $images[$i]["name"] = $fName;
        $image = NImage::fromFile($picPath.$fName);
        $w = (int)$image->getWidth();
        $h = (int)$image->getHeight();
        if ($w == 0) {
          $size = explode('x', $config["PROPICSIZE_LIST"]);
          $w = $size[0];             
          $h = $size[1];
        }
        $images[$i]["w"] = $w;
        $images[$i]["h"] = $h;
      }  
    }
    $this->template->images = $images;
    
    //todo doplnit vypis priloh
    $this->template->fileRows = array();
    
    //nactu si dalsi polozku v seznamu dle dane kategorie
    //zjistim korenovou uroven
    $catid = dibi::fetchSingle("SELECT catid FROM catalogs INNER JOIN catplaces ON (catid=capcatid) WHERE catmasid=0 AND catpathids NOT LIKE '|50|%' AND capproid=$id") ;
    $this->template->nextProductCatId = 0;
    $this->template->nextProduct = Null;
    if ($catid > 0) {
      //zjistim nazev kategorie
      $this->template->nextProductCatId = $catid;
      //vezmu vsechny 
      $this->template->nextProduct = dibi::fetch("SELECT * FROM products
      INNER JOIN catplaces ON (proid=capproid)
      INNER JOIN catalogs ON (catid=capcatid)
      WHERE
      prostatus=0 AND 
      catpathids LIKE '|$catid|%' AND 
      proname>%s", $product->proname, "
      GROUP BY proid
      ORDER BY proname");
      if (!$this->template->nextProduct) {
        //nic se nenaslo tak vezmu prvni ze seznamu
        $this->template->nextProduct = dibi::fetch("SELECT * FROM products
        INNER JOIN catplaces ON (proid=capproid)
        INNER JOIN catalogs ON (catid=capcatid)
        WHERE
        prostatus=0 AND 
        catpathids LIKE '|$catid|%'
        GROUP BY proid
        ORDER BY proname");
      }
    }

    //zda je to vzor - > nepujde koupit
    $str = substr($product->procode2, 0 , 5);
    $this->template->isVzor = ($str == 'vzor_');
  }
  
  protected function createComponentRequestForm() {
    //prihlasovaci form  
    $form = $this->createAppForm();
    $form->addText('reqfirname', 'Firma');
    $form->addText('reqname', 'Jméno a příjmení')
      ->addRule(NForm::FILLED, 'Prosím vyplňte jméno a příjmení.');
    $form->addText('reqmail', 'Váš email')
      ->setEmptyValue('vas@email')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Váš email.');
    $form->addText('reqphone', 'Telefonní číslo')
      ->addRule(NForm::FILLED, 'Prosím vyplňte telefonní číslo.');  
    $form->addTextArea('reqnote', 'Poznámka');
    
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setHtmlId('antispam')
      ->addRule(NForm::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);  
      
    $form->addSubmit('submit', 'Odeslat poptávku')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'requestSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
  public function requestSubmitted($form) {
    if ($form->isSubmitted()) {

      $vals = $form->getValues();
      $proid = $this->getParameter('id');
      $vals["reqproid"] = $proid;
      unset($vals["antispam"]);
      $reqs = new RequestsModel();

      $pattern = "/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/";

      if (preg_match($pattern, $vals->reqmail)) {
      } else {
        die();
      }

      if (strpos($vals->reqmail, 'test') !== false) {
        die();
      }

      $reqId = $reqs->insert($vals);
      $req = $reqs->load($reqId);
      $this->template->product = dibi::fetch("SELECT * FROM products WHERE proid=%s", $req->reqproid);
      $this->template->request = $reqs->load($reqId);
      $this->template->setFile(APP_DIR.'/../templates/Mails/mailProductRequest.phtml');

	  $mails = explode(',', $this->config["SERVER_MAILREQUEST"]);
      foreach ($mails as $mail) {
		  $this->mailSend($mail, 'Nová poptávka', $this->template);
      }

      /*$this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);
      $this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);
      $this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);
      $this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);
      $this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);
      $this->mailSend('<EMAIL>', 'Nová poptávka', $this->template);*/
      $this->flashMessage("Email s Vaší poptávkou byl odeslán.");
      $this->redirect('this');      
    }
  }    
}  
