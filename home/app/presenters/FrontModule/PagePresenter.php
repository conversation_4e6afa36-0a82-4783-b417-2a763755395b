<?php

final class Front_PagePresenter extends Front_BasePresenter {
  /********************* view default *********************/ 
  
  public function renderDetail($id) {
    $pages = new PagesModel();
    $pageData = $pages->load($id, 'urlkey');
    if ($pageData) {
      if ($pageData->pagid > 0) {
        $this->template->urlkey = $pageData->pagurlkey;
        $this->template->page = $pageData;
      }
    } else {
      throw new NBadRequestException('Stránka nenalezena', '404');
    }
    
    //zjistim jazyk stranky
    $arr = explode('-', $id);
    $isLngPage = False;
    if (strlen($arr[0])==2) {
      $lngs = $this->template->languages;
      $isLngPage = array_key_exists($arr[0], $lngs); 
    }
    
    if ($isLngPage) {
      $this->setView('detailLng');
      $this->template->lng = $arr[0];
      
      //nactu boky
      $block = dibi::fetch("SELECT * FROM pages WHERE pagurlkey=%s", $arr[0].'-menu-pics'); 
      if ($block) {
        $blocks = explode('#DRUHY_BLOK#', $block->pagbody); 
        $this->template->blockLeft = $blocks[0];
        $this->template->blockRight = $blocks[1];
        
        $this->template->h1 = $block->pagtitle;
        $this->template->h2 = $block->pagdescription;
        $alt = "";
        $pos = strpos($block->pagtitle, '<span>');
        if ($pos > 0) $alt = substr($block->pagtitle, 0, $pos); 
        $this->template->headAlt = $alt;
      }
    }
    if ($pageData->pagblock == 1) throw new NBadRequestException('Stránka nenalezena', '404');
    
    //zjistim jestli textova stranka je pouzita v seznamu ikonek na uvodni strance
    $this->template->menuIndex = dibi::fetch("SELECT * FROM menuindexs 
    LEFT JOIN catalogs ON (catid=meicatid) 
    LEFT JOIN products ON (procode=meiprocode) 
    WHERE meimasid=0 AND meistatus=0 AND meipagid=%i", $pageData->pagid);
    //pokud nejaky specialni typ doplnim potrebna data nastavim sablonu
    switch ($pageData->pagtypid) {
      case 103:  
        $this->template->prolinks = dibi::fetchAll("SELECT * FROM prolinks WHERE plistatus=0 ORDER BY pliname");
        $this->setView('prolink');
        break;  
      case 1:  
        //kontaktni form
        $this->setView('contact');
        break;
        
    }
    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $pageData->pagid); 
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $pageData->pagid);    
  }
  
  public function contactFormSubmitted (NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();
      unset($formVals["antispam"]);
      $id = $this->getParam('id');
      $pages = new PagesModel();
      $pageData = $pages->load($id, 'urlkey');  
      $body = 'Nový dotaz, vzkaz:<br />
      '.($pageData ? 'Stránka: '.$pageData->pagname.' ('.$this->presenter->link('//:Front:Page:detail', $id).')<br />' :'').'
      Jméno, Přijmení: '.$formVals["conname"].'<br />
      Email: '.$formVals["conmail"].'<br />
      Mobil: '.$formVals["congsm"].'<br />
      Poznámka: <br />
      '.nl2br($formVals["connote"]);
      $mail = new NMail();
      $mail->setFrom($this->config["SERVER_NAME"].' <'.$this->config["SERVER_MAIL"].'>');
      $mail->addReplyTo($formVals["conmail"]);
      $mail->addTo($this->config["SERVER_MAIL"]);
      $mail->setSubject('Nový dotaz, vzkaz');
      $mail->setHtmlBody($body);  
      try {
        $mail->send();
        $this->flashMessage("Váš vzkaz byl přijat. Děkujeme!");
        $this->redirect('this');
      } catch (InvalidStateException $e) {
        $someerr = true;
        $form->addError("Vzkaz se nepodařilo odeslat.");
      }
    }
  }   

  protected function createComponentContactForm() {
    $form = $this->createAppForm();
    
    $form->addText('conname', 'Jméno, příjmení', 30);
    
    $form->addText('conmail', "Platná emailová adresa (nutno vyplnit)", 20)
      ->addRule(NForm::FILLED, 'Prosím vyplňte platný email')
      ->addRule(NForm::EMAIL, 'Email nemá správný formát.');
    
    $form->addText('congsm', 'Telefon (mobil) pro rychlejší kontakt', 20);
    
    $form->addTextArea('connote', "Poznámka, dotaz či přání", 50, 8);
    
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
      ->addRule(NForm::FILLED, 'Prosím vyplňte antispamové číslo')
      ->setOption('description', 'test proti robotum')
      ->setHtmlId('antispam')
      ->addRule(NForm::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotům', $this->config["ANTISPAM_NO"]);
    
    //$form->setRenderer(new ScaffoldingRenderer); 
      
    $form->addSubmit('save', 'Odeslat')->getControlPrototype()->class('button');  
    $form->onSuccess[] = array($this, 'contactFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;   
  }
}