<?php

final class Front_CatalogPresenter extends Front_BasePresenter {
  /** @persistent */
  public $s = '';
  public $productsRows = array();
  /********************* view default *********************/ 
  
  public function renderDetailOld($id, $key) {
    $catalog = new CatalogsModel(); 
    $catalogData = $catalog->load($id);
    $key1 = $catalogData->catkeymaster;
    $key2 = (empty($catalogData->catkey) ? NStrings::webalize($catalogData->catname) : $catalogData->catkey) ;
    
    $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key1'=>$key1, 'key2'=>$key2));
  }
  
  public function renderDetail($id, $key1="", $key2="") {
    $this->s = $this->getParam('s');
    $catalog = new CatalogsModel();
    
    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);
    
    if ($catalogData === false) {
      throw new NBadRequestException('Katalog nenalezen', '404');
    } else {
      $key1Data = $catalogData->catkeymaster;
      $key2Data = (empty($catalogData->catkey) ? NStrings::webalize($catalogData->catname) : $catalogData->catkey) ;
      
      //pokud se zmenil klic presmeruju na novy
      if ($key1 != $key1Data || $key2 != $key2Data) $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key1'=>$key1Data, 'key2'=>$key2Data));
      
      $this->template->catalogData = $catalogData; 
    }

    //id aktualni kategorie
    $this->template->thisCatId = $catalogData->catid;
    $idPath = explode('|', trim($catalogData->catpathids, '|'));
    $menuCatalogL = array();
    foreach ($idPath as $catid) {
      $menuCatalogL[$catid] = dibi::fetchAll("SELECT * from catalogs WHERE catmasid=$catid AND catstatus=0 ORDER BY catorder");
      if ($catalogData->catid == $catid) break;  
    }

    $this->template->menuCatalogL = $menuCatalogL;
    
    $ses = NEnvironment::getSession('catlog');
    $ses->lastCatId = $catalogData->catid;
    
    
    //podrizene polozky
    $this->template->catalogSubItemsData = dibi::fetchAll("SELECT * from catalogs WHERE catmasid=$catalogData->catid  AND catstatus=0 ORDER BY catorder");
    
    //naplnim si do katalogu prislusne zbozi
    $product = new ProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    //sestavim WHERE
    switch ($this->s) {
       case '':
         $orderBy = "proorder";
         break;
       case 'name':
         $orderBy = " proname ASC";
         break;
       case 'name_':
         $orderBy = " proname DESC";
         break;
       case 'price':
         $orderBy = " IF(proprice".$this->userData->usrprccat.">0,proprice".$this->userData->usrprccat.",propricea) ASC"; 
         break;
       case 'price_':
         $orderBy = " IF(proprice".$this->userData->usrprccat.">0,proprice".$this->userData->usrprccat.",propricea) DESC";  
         break;    
    }
    $dataSource = $product->getDataSource($product->getSqlCatalogList($id, 'prostatus=0', $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $cache = NEnvironment::getCache($this->name);
    $cache["cat_productsRows"] = $this->template->productsData;
  }
  
  protected function createComponentAddBasketForm() { 
    $form = $this->createAppForm();
    //$data = $form->addContainer('data');
    $cache = NEnvironment::getCache($this->name);
    foreach ($cache["cat_productsRows"] as $row) {
      $container = $form->addContainer($row->proid);
      $container->addText('qty')
        ->setDefaultValue(1)
        ->addCondition(NForm::FILLED)
          ->addRule(NForm::INTEGER, 'Vyplňte prosím celé číslo.');
      $container->addSubmit('add_'.$row->proid, 'Košík')->getControlPrototype()->class('button');  
    }
    $form->onSuccess[] = array($this, 'addBasketFormSubmitted');
    return $form;
  }
  
  public function addBasketFormSubmitted (NAppForm $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();  
      $button = $form->isSubmitted();
      $proid = (int)substr($button->name, 4);
      $qty = (int)$vals[$proid]['qty'];
      if ($proid > 0 && $qty > 0) $this->redirect('Basket:add', $proid, $qty); 
    }
  }
  
  function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  }
    
}