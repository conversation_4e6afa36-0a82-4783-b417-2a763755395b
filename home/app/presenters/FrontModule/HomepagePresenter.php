<?php

final class Front_HomepagePresenter extends Front_BasePresenter {
  /********************* view default *********************/ 
  
  public function renderDefault() {
    //seznam produktu na uvodni strance
    //z nastaveni nactu kody zbozi
    $proCodesList = explode(',',$this->config["INDEX_PRODUCTLIST"]);
    $product = new ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $this->template->productsData = array();
    foreach ($proCodesList as $proCode) {
      $proCode = trim($proCode);
      if (!empty($proCode)) {
        $pro = $product->load($proCode, 'code');
        if ($pro !== FALSE) {
          if ($pro->prostatus == 0) $this->template->productsData[] = $pro;

        }
      }  
    }
    
        
    //nactu polozky menu
    $this->template->menuIndexsBig = dibi::fetchAll("
    SELECT * FROM menuindexs 
    LEFT JOIN pages ON (pagid=meipagid) 
    LEFT JOIN catalogs ON (catid=meicatid) 
    LEFT JOIN products ON (procode=meiprocode) 
    WHERE meimasid=0 AND meistatus=0 AND meibig=1 ORDER BY meiorder LIMIT 2");
    
    $this->template->menuIndexs = dibi::fetchAll("
    SELECT * FROM menuindexs 
    LEFT JOIN pages ON (pagid=meipagid) 
    LEFT JOIN catalogs ON (catid=meicatid) 
    LEFT JOIN products ON (procode=meiprocode) 
    WHERE meimasid=0 AND meistatus=0 AND meibig=0 ORDER BY meiorder");
  }
}
