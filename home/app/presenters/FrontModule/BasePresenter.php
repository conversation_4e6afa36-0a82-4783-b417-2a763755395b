<?php

abstract class Front_BasePresenter extends BasePresenter {
  const LOGIN_NAMESPACE = 'user';
  
  /** @persistent */
  public $backlink = '';
  
  /** p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ý uživatel */
  protected $userData;
  
  /** košík */
  protected $basketNamespace;
  
  /** identita */
  protected $user;
    
  protected function startup() {    
    parent::startup();
  
    //inicializace kosiku 
    $this->basketNamespace = NEnvironment::getSession('basket');
    $this->basketNamespace->setExpiration(0);
    if (!isset($this->basketNamespace->items)) $this->basketNamespace->items = array();
    if (!isset($this->basketNamespace->priceSum)) $this->basketNamespace->priceSum = 0;
    if (!isset($this->basketNamespace->priceSumTotal)) $this->basketNamespace->priceSumTotal = 0;
    
    //nactu uzivatele
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);
    $this->template->identity = $this->user;
    
    $this->userData = false;
    if ($this->user->isLoggedIn()) {
      $users = new UsersModel();
      $this->userData = $users->load($this->user->id);
    }
      
    if ($this->userData == false) {
      $this->userData = new DibiRow(array('usrid'=>0, 'usrprccat'=>'a'));
      if ($this->user->isLoggedIn()) $this->user->logout();
    }
    $this->template->userRow = $this->userData;
  
  }
  
  protected function beforeRender() {
    parent::beforeRender(); 
    $this->template->basketPriceSum = $this->basketNamespace->priceSum;
    $this->template->basketItemsCnt = count($this->basketNamespace->items);
   
    //vypis novinek v pravem menu
    $product = new ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $this->template->productNews = $product->fetchAll($product->getSqlList("protypid=21 AND prostatus=0", "proorder LIMIT 6"));
   
    //nactu bloky menu
    $catalogs = new CatalogsModel();
    $this->template->menuCatalog = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder");

    //horni horizontálni menu
    $this->template->menuTop = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
    //Informace o nákupu
    $this->template->menuInfoTitle = dibi::fetch("SELECT * FROM menus WHERE menid=10");
    $this->template->menuInfo = dibi::fetchAll("SELECT * FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=10 AND menstatus=0 ORDER BY menorder");
    
    
    //nactu textove bloky                                                                                                       
    $this->template->blockFooter = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='footer' AND pagstatus=0");
    
    
    //vypisu nejprodavanejsi zbozi
    $this->template->productsTopSale = dibi::fetchAll("SELECT proid, prokey, prokeymaster, procode, proname FROM products INNER JOIN products_salestat ON (proid=prsproid) ORDER BY COALESCE(prscnt,0) DESC, proorder ASC LIMIT 10");
  }  

  public function mainSearchFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      $vals = $form->getValues();  
      $searchParam = array('name'=>'');
      if (isset($vals["name"])) $searchParam["name"] = $vals["name"];
      $this->redirect('Search:default', $searchParam); 
    }
  }
  
  protected function createComponentSearchForm() { 
    $form = $this->createAppForm();
    $form->addtext("name", "", 10)
      ->addRule(NForm::FILLED, "Vyplňte hledaný text");
    $form->addSubmit('quickSearch', 'Hledat');
    $form->onSuccess[] = array($this, 'mainSearchFormSubmitted');
    return $form;
  }
  
  protected function createComponentUserLoginForm() {
    //prihlasovaci form  
    $form = $this->createAppForm();
    $form->addText('usrmail', 'Přihlašovací jméno')
      ->setEmptyValue('vas@email')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');
    $form->addPassword('usrpassw', 'Heslo')
      ->addRule(NForm::FILLED, 'Prosím vyplňte heslo.');  
    $form->addSubmit('submit', 'Přihlásit')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
  public function loginFormSubmitted($form) {
    try {
      $this->user->setExpiration(0);
      $this->user->login($form['usrmail']->getValue(), $form['usrpassw']->getValue(), self::LOGIN_NAMESPACE);
      $backlink = $this->backlink;
      $this->getApplication()->restoreRequest($backlink);
      $this->redirect('User:default');

    } catch (NAuthenticationException $e) {
      $this->flashMessage($e->getMessage(), 'err');
      $this->redirect('User:login');
    }
  }
  
  public function flashMessage($message, $type = "info") {
    if (is_array($message)) {
      $cnt = 1;
      $tMessage = "";
      foreach ($message as $key => $value) {
        if ($cnt % 2 == 0) {
          $tMessage .= ' '.$value.' ';
        } else {
          $tMessage .= $this->translator->translate($value);
        }
        $cnt++;
      }
    } else {
      $tMessage = $this->translator->translate($message);
    }
    return parent::flashMessage($tMessage, $type);
  }
  
  protected function createAppForm() {
    $form = new NAppForm();
    $form->setTranslator($this->translator);
    return $form;
  }
}