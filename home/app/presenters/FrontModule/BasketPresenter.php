<?php

final class Front_BasketPresenter extends Front_BasePresenter {
  
  /** @persistent */
  public $backlink = '';
  
  private $deliveryModeRows;
  private $productRows;
  
  /**
  * prepocita obsah kosiku 
  */
  private function recalc() {
    //kontrola zda vsechny polozky existuji - nejsou vyrazeny z nabidky
    $priceSum = 0;
    $priceSumVat = 0;
    $priceSumTotal = 0;
    $priceSumTotalVat = 0;
    $weightSum = 0;
    $products = array();
    $proIds = array();
    $this->basketNamespace->specDel = false;
    if (count($this->basketNamespace->items) > 0) {
      $product = new ProductsModel();
      $product->setPrcCat($this->userData->usrprccat);
      $setunset = false;
      foreach ($this->basketNamespace->items as $key => $value) {
        $row = $product->load($key);
        if (!$row) {
          unset($this->basketNamespace->items[$key]);
          continue;  
        }
        if ($row->proid == $key && $row->prostatus == 0) {
          //polozka nalezna v db a je aktivni
          $products[$key] = $row;
          //zkontroluju pocet skladem
          if ($this->config["CHECK_STOCK"] == 1) {
            if ((int)$value > (int)$row->proaccess) {
              if ((int)$row->proaccess == 0) {
                unset($this->basketNamespace->items[$key]);  
                $setunset = true;
              } else {
                $value = (int)$row->proaccess;
                $this->basketNamespace->items[$key] = $value;
                $this->flashMessage(array("U položky",$row->proname, "je skladem pouze", $row->proaccess, "ks"));
              } 
            }
          }   
          $priceSum += $row->proprice * $value;
          $priceSumVat += $this->getPriceVat($row->proprice, $row->provatid) * $value;
          $weightSum += $row->proweight * $value;
          
          $proIds[] = $row->proid;
          
          if ((int)$row->prooffer === 1 || (int)$row->probigsize === 1 || (double)$row->proweight === 0) {
            $this->basketNamespace->specDel =  true;
          }
        } else {
          //polozka nenalezena nebo neni aktivni
          $setunset = true;
          unset($this->basketNamespace->items[$key]);
        }  
      }
      //zjistim slevu - sleva se pocita jen pro cenovou kategorii A
      $discount = (double)0;
      if ($this->userData->usrprccat == 'a') {
        //zjistim slevu nastavenou zakaznikovi
        if ($this->userData->usrid > 0) {
          $discount = (double)$this->userData->usrdiscount;
        }
        //zjistim mnozstevni slevu
        $disc = (double)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE $priceSum BETWEEN disfrom AND disto AND disstatus=0");
        if ($disc > $discount) $discount = $disc;
      }
      $this->basketNamespace->discountPer = $discount;
      $this->basketNamespace->discountVal = $priceSum * ($discount / 100);
       
      $priceSumTotal = $priceSum - $this->basketNamespace->discountVal; 
      $priceSumTotalVat = $priceSumVat - $this->getPriceVat($this->basketNamespace->discountVal, 0);
      
      if($this->basketNamespace->delid > 0 && $this->basketNamespace->specDel == False) {
        $deliverymode  = new DeliveryModesModel();
        $dataRow = dibi::fetch("SELECT delid, delname, CASE WHEN ".$this->basketNamespace->priceSum." BETWEEN dellimitfrom AND dellimitto THEN 0 ELSE delprice END AS  delprice FROM deliverymodes WHERE delid=%i", $this->basketNamespace->delid);
        $priceSumTotal += $dataRow->delprice; 
        $priceSumTotalVat += $this->getPriceVat($dataRow->delprice, 0); 
      }
      
      if ($setunset) $this->flashMessage("Některé položky byly z košíku odstraněny, neboť byly vyřazeny z nabídky, nebo již nejsou skladem.");     
    }
    $this->basketNamespace->priceSum = $priceSum;
    $this->basketNamespace->priceSumVat = $priceSumVat;
    $this->basketNamespace->priceSumTotal = $priceSumTotal;
    $this->basketNamespace->priceSumTotalVat = $priceSumTotalVat;
    $this->basketNamespace->weightSum = $weightSum;
    $this->productRows = $products;
    if (count($proIds) > 0) {
      $this->basketNamespace->proids = '["'.implode('","', $proIds).'"]';  
    } else {
      $this->basketNamespace->proids = '';  
    }
  }
  
  /**
  * vymaze polozku z kosiku
  *    
  * @param integer $proid id polozky
  */
  public function actionDelete($proid) {
    if (array_key_exists($proid, $this->basketNamespace->items)) {
      unset($this->basketNamespace->items[$proid]);
    }
    $this->redirect('default');      
  }
  
  /**
  * prida polozku do kosiku
  * 
  * @param integer $proid id polozky
  * @param integer $count pocet kusu
  */
  public function actionAdd($proid, $count=NULL) {
    if ($count === NULL) $count = 1;
    if (!array_key_exists($proid, $this->basketNamespace->items)) {
      //kontrola zda je zbozi skladem a zda je aktivni
      $products = new ProductsModel();
      $row = $products->load($proid);
      if ($row) {
        //polozka nalezna v db a je aktivni
        if ((int)$row->prostatus != 0) {
          $this->flashMessage("Zboží nejde přidat do košíku, je momentálně vyřazeno z nabídky.", "err");
        } else if ((int)$row->proaccess == 0 && $this->config["CHECK_STOCK"] == 1) {  
          $this->flashMessage("Zboží nejde přidat do košíku, není momentálně skladem.", "err");
        } else {
          if ((int)$row->proaccess < $count && $this->config["CHECK_STOCK"] == 1) {
            $this->flashMessage(array("Položka byla přídána do košíku, ale v menším počtu, skladem je pouze",$row->proaccess,"ks"), "err"); 
            $count = (int)$row->proaccess;
          }  
          $this->basketNamespace->items[$proid] = $count;  
        }
      }
    }
    $this->redirect('default');   
  }
  
  /********************* view default *********************/ 
  public function renderSendPayRequest($ordid, $key) {
    $orders = new OrdersModel();
    $order = $orders->load($ordid);
    if ($order) {
      if ($order->ordpayreqs == 0) {
        //mailuju
        $mailTemplate = $this->createTemplate();
        $mailTemplate->orderRow = $order;
        $mailTemplate->setFile(APP_DIR.'/../templates/Mails/mailOrderPayRequest.phtml');
        try {
          //mailuju obchodnikovi
          $this->mailSend($this->config["SERVER_MAILFORPAY"], "Žádost o platbu PayPal", $mailTemplate);
        } catch (InvalidStateException $e) {
          //$this->flashMessage("Nepodařilo se ale odeslat email o nové objednávce", "err");
          $this->template->errorMsg = "Nepodařilo se ale odeslat email s žádostí o platbu.";
        }
        
        $orders->update($ordid, array('ordpayreqs'=>1));   
      } else {
        $this->template->errorMsg = "Žádost o instrukce k platbě už byla jednou odeslána.";
      } 
    }
    //$this->template->key = substr(md5($ordid.$mailTemplate->orderRow->orddatec), 0, 4);  
  }
  
  public function renderAccepted() {
    $orders = new OrdersModel();
    $ordid = $this->basketNamespace->ordid; 
    $order = $orders->load($ordid);
    if ($order) {
      $order->items = dibi::fetchAll("SELECT orditems.*, catpath, catpathids, procode, proname FROM orditems 
INNER JOIN products ON (oriproid=proid)
INNER JOIN catplaces ON (oriproid=capproid)
INNER JOIN catalogs ON (catid=capcatid)
WHERE oriordid=%i GROUP BY oriid", $order->ordid);
      $priceSum = 0;
      $priceSumVat = 0;
      $price = 0;
      $proIds = array();
      foreach ($order->items as $key=>$row) {
        $price = 0;
        $proIds[] = $row->oriproid;
        $vatType = (string)$this->config["PRICEVAT"];
        $price = $row->oriprice * $row->oriqty;
        $priceSum += $price;
        $order->items[$key]->oripricenovat = $price;
        $priceSumVat += $this->getPriceVat($row->oriprice, $row->orivatid) * $row->oriqty;
      }
      
      $order->proids = '["'.implode('","', $proIds).'"]';
      $order->ordpricenovat = $priceSum;
      $order->ordpriceinclvat = $priceSumVat;
      $order->orddelprice = dibi::fetchSingle("SELECT oriprice FROM orditems WHERE oritypid=1 AND oriordid=%i", $order->ordid);
    }
    $this->template->payment = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $this->template->delivery = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $this->template->payment->delmasid);
    
    $this->template->order = $order;
    
    $this->template->blockOrderAccepted = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='objednavka_odeslana' AND pagstatus=0");
    
  }
  
  public function renderDefault() {
    $this->basketNamespace->delid = 0;
    $this->recalc();
    $form = $this->getComponent("basketForm");
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
    if (!$form->isSubmitted()) {
      $delivModes = new DeliveryModesModel();
      $this->template->deliveryModeRows = $delivModes->getEnumDelModesByPrice($this->basketNamespace->priceSum);
      $this->template->productRows = $this->productRows;
      $this->template->basket = $this->basketNamespace;
            
      //zjistim nejblizsi slevu 
      $nextDisc = dibi::fetch("SELECT dispercent, disfrom - ".$this->basketNamespace->priceSum." AS diff FROM discounts WHERE disstatus=0 AND disfrom > ".$this->basketNamespace->priceSum);
      //zjistim dopravu zdarma pro vsechny zeme
      //vezmu aktivni zeme
      $enums = new EnumcatsModel();
      $couArr = $enums->getEnumCountries();
      $delFreeArr = array();
  
      foreach ($couArr as $key => $value) {
        //pro kazdou zemi zjistim zda ma dopravu zdarma
        $del = dibi::fetch("SELECT * FROM deliverymodes WHERE delmasid > 0  AND delcouid=$key AND delstatus=0 AND ".$this->basketNamespace->priceSum." BETWEEN dellimitfrom AND dellimitto");
        if ($del) { 
          $delFreeArr[$key] = $del;
          $delFreeArr[$key]['mas'] = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i",$del->delmasid) ;
        }
      }
  
      $nextDelFreeArr = array();
      foreach ($couArr as $key => $value) {
        //pro kazdou zemi zjistim za kolik ma dopravu zdarma, pokudji uz zdarma nema
        //if (!isset($delFreeArr[$key])) {
          $del = dibi::fetch("SELECT *, dellimitfrom - ".$this->basketNamespace->priceSum." AS diff FROM deliverymodes WHERE  delmasid > 0 AND delcouid=$key AND delstatus=0 AND dellimitfrom > ".$this->basketNamespace->priceSum);
          if ($del) { 
            $nextDelFreeArr[$key] = $del;
            $nextDelFreeArr[$key]['mas'] = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i",$del->delmasid) ;
          }
        //}  
      }

      $this->template->nextDisc = $nextDisc;
      $this->template->delFree = $delFreeArr;
      $this->template->nextDelFree = $nextDelFreeArr;
      $this->template->enum_countries = $couArr;
    }  
  }
  
  public function renderOrderDelMode() {
    if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');
    //nejsou vyplneny kontaktni udaje, vratim na kontaktni udaje
    if (!isset($this->basketNamespace->contact)) $this->redirect('Basket:orderContact');
    
    $delmasid = (int)$this->getParam('m');
    $delid = (int)$this->getParam('d');
    
    
    if ($delmasid == 0) {
      $delModes = dibi::fetchAll("SELECT * FROM deliverymodes WHERE delstatus=0 AND delmasid=0 AND ".$this->basketNamespace->weightSum." BETWEEN delweightlimitfrom AND delweightlimitto ".($this->basketNamespace->specDel ? " AND delspecdel=1" : '')." ORDER BY delorder");   
      if ($delModes) {
        $firstItem = current($delModes);  
        $delmasid = (int)$firstItem->delid;
        $this->params['m'] = $delmasid;
      }  
    }
    
    if ($delid == 0 && $delmasid > 0) {
      //vezmu prvni dopravu v seznamu pro dany delmasid
      //doplnim dopravy co odpovidaji cene objednavky, cilove zemi 
      $delModes = dibi::fetchPairs("SELECT delid, delname FROM deliverymodes WHERE delmasid=$delmasid AND delstatus=0 ORDER BY delorder");
      $delid = (int)key($delModes);
      $this->params['d'] = $delid;
    }
    $this->template->delmasid = $delmasid;
    $this->template->delid = $delid;
    $this->basketNamespace->delid = $delid;
    $this->template->delMode = dibi::fetch("SELECT delid, delmasid, delname, CASE WHEN ".$this->basketNamespace->priceSum." BETWEEN dellimitfrom AND dellimitto THEN 0 ELSE delprice END AS  delprice, delspecdel FROM deliverymodes WHERE delid=%i", $delid);
    $this->recalc();
    $this->template->productRows = $this->productRows;
    $this->template->basket = $this->basketNamespace;
    $enums = new EnumcatsModel();
    $this->template->enum_countries = $enums->getEnumCountries();
  }

  protected function createComponentOrderDelModeForm() {  
    $form = $this->createAppForm();
  
    $couid = $this->basketNamespace->contact['ordicouid'];
    //zjistim region
    $regid = (int)dibi::fetchSingle("SELECT enutagnum FROM enumcats WHERE enutypid=1 AND enuid=%i", $couid);
    
    $delmasid = (int)$this->getParam('m');
    $delid = (int)$this->getParam('d');
    
    if ($delid === 0 && isset($this->basketNamespace->delid)) {
      if ($this->basketNamespace->delid > 0) {
        $delid = $this->basketNamespace->delid;
        if ($delmasid === 0) $delmasid = (int)dibi::fetchSingle("SELECT delmasid FROM deliverymodes WHERE delid=%i", $delid);
      }
    }
    
    //doplnim dopravy co odpovidaji hmotnosti objednavky
    $delModes = dibi::query("
    SELECT delid, delmasid, delname FROM deliverymodes WHERE 
    delmasid IN (SELECT delid FROM deliverymodes WHERE delstatus=0 AND delmasid=0 AND ".($this->basketNamespace->specDel ? "delspecdel=1" : $this->basketNamespace->weightSum." BETWEEN delweightlimitfrom AND delweightlimitto").") AND delcouid=$regid AND delstatus=0  ORDER BY delmasid, delorder")
      ->fetchAssoc('delmasid');   
    
    
    $delMasIds = implode(',', array_keys($delModes));
    if ($delModes == false) $delMasIds = "-1";
    $delMasModes = dibi::query("SELECT * FROM deliverymodes WHERE delstatus=0 AND delid IN ($delMasIds) AND ".($this->basketNamespace->specDel ? "delspecdel=1" : $this->basketNamespace->weightSum." BETWEEN delweightlimitfrom AND delweightlimitto")." ORDER BY delorder")->fetchPairs('delid', 'delname');
    
    $form->addGroup('Způsob dodání');
    //naplnim radio s typy dopravy
    $form->addRadioList('delmasid', "Způsob dopravy", $delMasModes)
      ->setTranslator(Null)
      ->setHtmlId('delmasid')
      ->setDefaultValue($delmasid)
      ->addRule(NForm::FILLED, "Způsob dopravy musí být vyplněn");
    //naplnim radio se způsoby platby
    $delModes = dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid=$delmasid AND delcouid=$regid AND delstatus=0 ORDER BY delmasid, delorder")->fetchPairs();
    $form->addRadioList('orddelid', "Způsob platby", $delModes)
      ->setTranslator(Null)
      ->setHtmlId('orddelid')
      ->setDefaultValue($delid)
      ->addRule(NForm::FILLED, "Způsob platby musí být vyplněn");
    /*
    $form->addCheckbox("register", "Chci se zaregistrovat a získat slevu ".$this->config["DEFAULT_DISCOUT"]."% na všechny mé nákupy")
      ->addCondition(NForm::EQUAL, 1)
        ->toggle("regForm");
    
    $form->addPassword('usrpassw', 'Heslo', 20)
      ->addConditionOn($form["register"], NForm::EQUAL, 1)
        ->addRule(NForm::FILLED, 'Prosím vyplňte heslo.')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);      
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addConditionOn($form["register"], NForm::EQUAL, 1)
        ->addRule(NForm::FILLED, 'Prosím vyplňte heslo podruhé.')
      ->addCondition(NForm::FILLED) 
        ->addRule(NForm::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"]);
    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách")
      ->setDefaultValue(True);  
    */  
    $form->addSubmit('makeorder', 'Odeslat objednávku')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');    
    return $form;  
          
  }
  
  public function renderOrderContact() {
    if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');
    if (isset($this->basketNamespace->contact)) {
      $form = $this->getComponent("orderContactForm");
      $form->setDefaults($this->basketNamespace->contact);  
    } else {  
      //predvyplnim kontaktni udaje pokud je prihlaseny a pokud je ma vyplneny
      $LoggedUser = $this->userData;
      if ($LoggedUser->usrid > 0) {
        $defVals = array(
          'ordiname' => $LoggedUser->usriname,
          'ordilname' => $LoggedUser->usrilname,
          'ordifirname' => $LoggedUser->usrifirname,
          'ordistreet' => $LoggedUser->usristreet,
          'ordistreetno' => $LoggedUser->usristreetno,
          'ordicity' => $LoggedUser->usricity,
          'ordipostcode' => $LoggedUser->usripostcode,
          'ordicouid' => $LoggedUser->usricouid,
          'ordic' => $LoggedUser->usric,
          'orddic' => $LoggedUser->usrdic,
          'ordstname' => $LoggedUser->usrstname,
          'ordstlname' => $LoggedUser->usrstlname,
          'ordstfirname' => $LoggedUser->usrstfirname,
          'ordststreet' => $LoggedUser->usrststreet,
          'ordststreetno' => $LoggedUser->usrststreetno,
          'ordstcity' => $LoggedUser->usrstcity,
          'ordstpostcode' => $LoggedUser->usrstpostcode,
          'ordstcouid' => $LoggedUser->usrstcouid,
          'ordtel' => $LoggedUser->usrtel,
          'ordmail' => $LoggedUser->usrmail,
          'shipto' => ($LoggedUser->usrstname != ""),
        );
        $form = $this->getComponent("orderContactForm");
        $form->setDefaults($defVals);
      }
    }      
  }
  
  protected function createComponentOrderContactForm() {
    $order = new OrdersModel(); 
    $enums = new EnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $form = $this->createAppForm();
    
    //$form->getElementPrototype()->id = 'makeOrderForm';   
    $form->addGroup('Fakturační adresa');
    
    $form->addText("ordiname", "Jméno", 60)
      ->addRule(NForm::FILLED, 'Prosím vyplňte jméno.');
    
   $form->addText("ordilname", "Přijmení", 60)
      ->addRule(NForm::FILLED, 'Prosím vyplňte přijmení.');   
    
    $form->addText("ordifirname", "Název firmy", 60);     
          
    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(NForm::FILLED, 'Prosím vyplňte ulici.');
    
    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(NForm::FILLED, 'Prosím vyplňte číslo popisné.');  
       
    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(NForm::FILLED, 'Prosím vyplňte město, obec.');  
      
    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(NForm::FILLED, 'Prosím vyplňte PSČ.');
    
    $form->addSelect("ordicouid", "Země", $enumCountries)
      ->setTranslator(Null)
      ->setDefaultValue(key($enumCountries))
      ->setHtmlId('ordicouid')
      ->addRule(NForm::FILLED, 'Prosím vyplňte zemi.')
      ->addCondition(NForm::EQUAL, 1)
        ->toggle("icDic"); 
      
    $form->addText("ordtel", "Telefon", 20)
      ->addRule(NForm::FILLED, 'Prosím vyplňte %label.');    
      
    $form->addText("ordmail", "Email", 20)
      ->setEmptyValue('vas@email')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(NForm::EMAIL, 'Email nemá správný formát');
    
    $form->addText("ordic", "IČ", 20)
      ->addConditionOn($form["ordifirname"], Nform::FILLED)
        ->addRule(NForm::FILLED, 'Pokud jste vyplnili Název firmy, je nutné vyplnit i IČ');    
        
    $form->addCheckbox('dicempty', 'Nejsem plátce DPH');
    
    $form->addText("orddic", "DIČ", 20)
      ->addConditionOn($form["ordifirname"], Nform::FILLED)
        ->addConditionOn($form["dicempty"], Nform::EQUAL, FALSE)
          ->addRule(NForm::FILLED, 'Je nutné vyplnit DIČ nebo zaškrtnout volbu "Nejsem plátce DPH"');
    
    $form["dicempty"]
      ->addConditionOn($form["ordifirname"], Nform::FILLED)
        ->addConditionOn($form["orddic"], NForm::FILLED)
          ->addRule(NForm::EQUAL, 'Je nutné vyplnit DIČ nebo zaškrtnout volbu "Nejsem plátce DPH"', FALSE);
        
    $form->addGroup('Fakturační adresa')
      ->setOption('embedNext', TRUE);
    
    $form->addCheckbox('shipto', 'Dodací adresa je jiná než fakturační')
      ->addCondition(NForm::EQUAL, TRUE) // conditional rule: if is checkbox checked...
        ->toggle('sendBox'); // toggle div #sendBox
            
    // subgroup
    $form->addGroup()
      ->setOption('container', NHtml::el('div')->id('sendBox'));  
    
    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte jméno.');
    
    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte přijmení.');
    
    $form->addText("ordstfirname", "Název firmy", 60);         
      
    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte ulici.'); 
    
    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte číslo popisné.'); 
          
    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte město, obec.');
      
    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte PSČ.');
        
    $form->addSelect("ordstcouid", "Země", $enumCountries)
      ->addConditionOn($form["shipto"], NForm::FILLED, True)
        ->addRule(NForm::FILLED, 'Prosím vyplňte zemi.');        
    
    $form->addGroup(Null);
    
    if (!$this->user->isLoggedIn()) {
      $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
        ->setOption('description', 'test proti robotum')
        ->setHtmlId('antispam')
        ->addRule(NForm::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);  
    }
    
    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);
    
    $form->addSubmit('submit', 'Pokračovat v objednávce')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'orderContactFormSubmitted');
    //$form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');    
    return $form;
  }  
  
  public function orderContactFormSubmitted(NAppForm $form) {
    if ($form->isSubmitted()) {  
      $formVars = $form->getValues();
      //unset 
      unset($formVars["shipto"]);
      unset($formVars["antispam"]);
      
      $this->basketNamespace->contact = $formVars;
      $this->redirect("Basket:orderDelMode");
    }
  }
  
  public function basketFormSubmitted(NAppForm $form) {
    if ($form->isSubmitted()) {  
      $formVars = $form->getValues();
      $datachanged = false; 
      foreach ($formVars as $key => $value) {
        if (substr($key, 0, 6) == 'count_')  {
          $proid = substr($key, 6);
          if ($value > 0) {
            $this->basketNamespace->items[$proid] = $value;
            $datachanged = true; 
          } else {
            $datachanged = true;
            unset($this->basketNamespace->items[$proid]); 
          }
        }
      }
      //$this->basketNamespace->delid = $formVars["delid"];
      
      if($form["recalc"]->isSubmittedBy()) {
        //if ($datachanged == true) $this->flashMessage("Obsah Vašeho košíku byl úspěšně aktualizován"); 
        $this->redirect("this");
      } else if($form["makeorder"]->isSubmittedBy()) {
        $this->redirect("Basket:orderContact");
      }  
    }
  }
  
  protected function createComponentBasketForm() { 
    $form = $this->createAppForm();
    $product = new ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    foreach ($this->basketNamespace->items as $key => $count) {
      $row = $product->load($key);
      if ($row->proid > 0  && $row->prostatus == 0) {
        $form->addText("count_".$row->proid, "", 3)
          ->addRule(NForm::INTEGER, "Počet kusů musí být celé číslo")
          ->setDefaultValue($count);
      }
    }
    /*
    $subItems = array();
    $delivModes = new DeliveryModesModel();
    $deliveryModeRows = $delivModes->getEnumDelModesByPrice($this->basketNamespace->priceSum); 
    foreach ($deliveryModeRows as $row) {
      
      $selval = 0;
      foreach ($row["subItems"] as $subRow) {
        $subItems[$subRow->delid] = $subRow->delname." (".$subRow->delprice." ".$this->config["CURR_CODE"].")";
      }
      $delItems[$row->delname] = $subItems;
    }
    $form->addselect("delid", "Doprava", $subItems)
      ->addRule(NForm::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte');
    if ($this->basketNamespace->delid > 0) $form["delid"]->setDefaultValue($this->basketNamespace->delid);         
    */
    $form->addSubmit('recalc', 'Přepočítat')->getControlPrototype()->class('button'); 
    $form->addSubmit('makeorder', 'Objednat')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'basketFormSubmitted');
    return $form;
  }  
  
  public function makeOrderFormSubmitted(NAppForm $form) {
    if ($form->isSubmitted()) {
      if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');
      //nejsou vyplneny kontaktni udaje, vratim na kontaktni udaje
      if (!isset($this->basketNamespace->contact)) $this->redirect('Basket:orderContact');
      
      $LoggedUser = $this->userData;
      $formVars = $form->getValues();
      $formVars = array_merge((array)$formVars, (array)$this->basketNamespace->contact);
      unset($formVars["antispam"]); 
      unset($formVars["shipto"]);
      unset($formVars["delmasid"]);
      unset($formVars["dicempty"]);
      
      $formVars["ordusrid"] = $LoggedUser->usrid;
      $formVars["orddiscpercent"] = (int)$this->basketNamespace->discountPer;
      $formVars["ordweight"] = $this->basketNamespace->weightSum;
        
      $orders = new OrdersModel();
    
      //ulozim hlavicku objednavky
      $ordid = $orders->insert($formVars);
      if ($ordid > 0 && $this->basketNamespace->delid > 0) {
        //$this->recalc();
        $ordItems = new OrdItemsModel();
        $ordItemsVals = array(
        'oriordid' => $ordid,
        );
        //vlozim polozky
        $product = new ProductsModel();
        $product->setPrcCat($LoggedUser->usrprccat);   
        foreach ($this->basketNamespace->items as $id => $cnt) {
          $row = $product->load($id);
          if ($row->proid == $id && $row->prostatus == 0) {
            //polozka nalezena v db a je aktivni - vlozim mezi polozky objednavky
            $ordItemsVals['oriproid'] = $id;
            $ordItemsVals['oriprocode'] = $row->procode;
            $ordItemsVals['oriprocode2'] = $row->procode2;
            $ordItemsVals['oritypid'] = 0;
            $ordItemsVals['oriname'] = $row->proname;
            $ordItemsVals['oriprice'] = $row->proprice;
            $ordItemsVals['orivatid'] = $row->provatid;
            $ordItemsVals['oricredit'] = $row->procredit; 
            $ordItemsVals['oriqty'] = $cnt; 
            $ordItemsVals['oriprobigsize'] = $row->probigsize; 
            $ordItemsVals['oriprooffer'] = $row->prooffer; 
            $ordItems->insert($ordItemsVals);
          }
          $ordItemsVals['oriprocode'] = Null;
          $ordItemsVals['oriprocode2'] = Null;  
        }
        //vlozim postovne
        //eliveryModes = new DeliveryModesModel();
       
        $row = $this->template->delMode = dibi::fetch("SELECT delid, delname, CASE WHEN ".$this->basketNamespace->priceSum." BETWEEN dellimitfrom AND dellimitto THEN 0 ELSE delprice END AS  delprice FROM deliverymodes WHERE delid=%i", $this->basketNamespace->delid);
        $ordItemsVals['oritypid'] = 1;
        $ordItemsVals['oriproid'] = 0;
        $ordItemsVals['oriname'] = "dopravné a balné";
        $ordItemsVals['oriprice'] = ($this->basketNamespace->specDel ? 0 : $row->delprice);
        $ordItemsVals['orivatid'] = Null;
        $ordItemsVals['oricredit'] = 0; 
        $ordItemsVals['oriqty'] = 1; 
        $ordItemsVals['oriprobigsize'] = 0; 
        $ordItemsVals['oriprooffer'] = 0; 
        $ordItems->insert($ordItemsVals);

        $orders->recalcOrder($ordid);
        $this->basketNamespace->ordid = $ordid;
        //vymazu kosik
        $specDel = $this->basketNamespace->specDel;
        $basket = $this->basketNamespace;
        $this->basketNamespace->remove();
        $this->basketNamespace = NEnvironment::getSession('basket');
        $this->basketNamespace->setExpiration(0);
        $this->basketNamespace->ordid = $ordid;
        
        //ulozim vyplnene kontaktni udaje do uctu uzivatele pokud nema vypleneny
        /*
        if ($LoggedUser->usrid > 0) {
          if ($LoggedUser->usriname == "") {
            //predplnim udaje
            $userVals = array(
              'usrstname' => $formVars["ordstname"],
              'usrstlname' => $formVars["usrstlname"],
              'usrstfirname' => $formVars["usrstfirname"],
              'usrststreet' => $formVars["ordststreet"],
              'usrststreetno' => $formVars["ordststreetno"],
              'usrstcity' => $formVars["ordstcity"],
              'usrstpostcode' => $formVars["ordstpostcode"],
              'usrstcouid' => $formVars["ordstcouid"],
              'usrtel' => $formVars["ordtel"],
              'usric' => $formVars["ordic"],
              'usrdic' => $formVars["orddic"],
              'usriname' => $formVars["ordiname"],
              'usrilname' => $formVars["usrilname"],
              'usrifirname' => $formVars["usrifirname"],
              'usristreet' => $formVars["ordstireet"],
              'usristreetno' => $formVars["ordistreetno"],
              'usricity' => $formVars["ordicity"],
              'usripostcode' => $formVars["ordipostcode"],
              'usricouid' => $formVars["ordicouid"],
            );
            $users = new UsersModel();
            if ($users->update($LoggedUser->usrid, $userVals)) {
              
            }
          }
        }
        */
        //vystavim fa
        $orders->makeInvoice($ordid);
        
        $order = $orders->load($ordid);
        
        //odmailuju
        $mailTemplate = $this->createTemplate();
        $mailTemplate->orderRow = $order;
        $delModes = new DeliveryModesModel();
        $enums = new EnumcatsModel();
        $mailTemplate->basket = $basket;
        $mailTemplate->enum_delModes = $delModes->getEnumDelModes();
        $mailTemplate->payMode = $delModes->load($order->orddelid);
        $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
        $mailTemplate->enum_Countries = $enums->getEnumCountries();
        $mailTemplate->ordItemRows = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
        $mailTemplate->ordSpecDel = ((int)dibi::fetchSingle("SELECT COUNT(*) FROM orditems WHERE oriordid=%i", $ordid, " AND oritypid=0 AND (oriprobigsize=1 OR oriprooffer=1)") > 0);
        $mailTemplate->orderRow = $orders->load($ordid);
        $mailTemplate->key = substr(md5($ordid.$mailTemplate->orderRow->orddatec), 0, 4);
        $mailTemplate->setTranslator($this->translator);
        $mailTemplate->lang = $this->lang;
        $mailTemplate->setFile(APP_DIR.'/../templates/Mails/mailOrderCreated.phtml');
        try {
          //mailuju zakaznikovi
          $mails = explode(',', $this->config["SERVER_MAILORDERS"]);
          $mailOrders = $mails[0]; 
          $this->mailSend($order->ordmail, "Objednávka č."." ".$order->ordcode, $mailTemplate, $mailOrders);
          //mailuju obchodnikovi
          foreach ($mails as $mail) {
            $this->mailSend($mail, "Nová objednávka č. ".$order->ordcode, $mailTemplate);
          }
        } catch (InvalidStateException $e) {
          $this->flashMessage("Nepodařilo se ale odeslat informační email o nové objednávce", "err");
        }
        
        //$this->flashMessage("Objednávka byla odeslána.");
        $this->redirect("accepted");
      }  
    }
  } 
}