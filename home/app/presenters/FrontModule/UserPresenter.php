<?php

final class Front_UserPresenter extends Front_BasePresenter {
  
  protected function startup() {
    parent::startup();
    // autorizace zakaznika 
    if ($this->action != "login" && $this->action != 'add' && $this->action != 'sendPassword') {  
      if (!$this->user->isLoggedIn()) {
        if ($this->user->getLogoutReason() === IUserStorage::INACTIVITY) {
          $this->flashMessage('Byl/a jste odhlášen/a z důvodu delší neaktivity.');
        }
        $backlink = $this->getApplication()->storeRequest();
        $this->redirect('User:login', $backlink);
      }
    }
  }
   
  public function actionLogin($backlink) {
    if ($this->user->isLoggedIn()) $this->redirect('User:default');
  }
  
  public function actionLogout() {
    $user = $this->user;
    $user->logout();
    $this->flashMessage("Odhlášení proběhl<PERSON> ú<PERSON>.");
    $this->redirect('User:login');
  }  

  /********************* view default *********************/ 
  
  public function renderDefault() {
    $user = new UsersModel();
    
    $userRow = $user->load($this->user->getIdentity()->id);
    $this->template->userRow = $userRow;
    
    //otevrene objednavky
    $this->template->openedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=$userRow->usrid AND ordstatus NOT IN (4,5) ORDER BY ordid DESC");
    //uzavrene objednavky
    $this->template->closedOrders = dibi::fetchAll("SELECT * FROM orders WHERE ordusrid=$userRow->usrid AND ordstatus IN (4,5) ORDER BY ordid DESC");
    $orders = new OrdersModel();
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }
  
  /********************* view add, edit *********************/ 
  public function renderAdd() {
    $form = $this->getComponent('userAddForm');
    $form['save']->caption = 'Registrovat';
    $this->template->form = $form;
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
  }
  
  public function renderEdit() {
    $id = NEnvironment::getUser()->getIdentity()->id;
    $form = $this->getComponent("userEditForm");
    
    if (!$form->isSubmitted()) {
      $user = new UsersModel();
      $userRow = $user->load($id);
      if (!$userRow) {
        throw new NBadRequestException('Záznam nenalezen');
      }
      
      
      $userRow["stadr"] = ($userRow["usrstname"] != "");
      $form->setDefaults($userRow);
    }
  }
  
  public function renderOrder($id) {
    $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order===false) throw new NBadRequestException('Objednávka nenalezena', '404');
    //kontrola zda tato objednavka patri prihlasenemu
    if ($this->userData->usrid != $order->ordusrid) throw new NBadRequestException('Objednávka nenalezena', '404');
    $this->template->order = $order;
    
    $enums = new EnumcatsModel();
    $delModes = new DeliveryModesModel();
    $this->template->enum_delModes = $delModes->getEnumDelModes();
    $this->template->payMode = $delModes->load($order->orddelid);
    $this->template->delMode = $delModes->load($this->template->payMode->delmasid);
    $this->template->enum_countries = $enums->getEnumCountries();
    $this->template->ordItems = dibi::fetchAll("SELECT * from orditems WHERE oriordid=%i", $order->ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
  }                                                                                                          
  
  public function sendPasswordFormSubmitted($form) {
    if ($this->user->isLoggedIn()) $this->redirect('User:login', $backlink);
    
    //kontrola zda takovy email existuje v DB
    if ($form['submit']->isSubmittedBy()) {
      $user = new UsersModel();
      $formVars = $form->getValues();
      $dataRow = $user->load($formVars["usrmail"], 'mail');
      if ($dataRow["usrid"] > 0) {
        //zmenim heslo a poslu email
        $newPassw = $this->getVerifyCode();
        $vals = array('usrpassw' => md5($newPassw));
        if ($user->update($dataRow->usrid, $vals)) {
          //odmailuju nove heslo
          $this->template->newPassword = $newPassw;
          $this->template->setFile(APP_DIR.'/../templates/Mails/mailUserSendPassword.phtml');
          
          if (!$this->mailSend($dataRow->usrmail, $this->translator->translate("Žádost o nové heslo"), $this->template)) $form->addError('Nové heslo se nepodařilo odeslat');
          $this->flashMessage("Email s novým heslem byl odeslán.");
          $this->redirect('this');
        } else {
          $form->addError('Nové heslo se nepodařilo nastavit');
        }
        
      } else {
        $form->addError('Zadaný email nebyl nalezen'); 
      }    
    }
  }
  
  public function userFormSubmitted(NAppForm $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = 0;
      //$identity = NEnvironment::getUser()->setNamespace(self::LOGIN_NAMESPACE);
      if ($this->user->isLoggedIn()) {
        $id = $this->user->getIdentity()->id;
      }
      $formVars = $form->getValues();
      //kontrola duplicity emailu
      $cnt = dibi::fetchSingle("SELECT COUNT(usrid) FROM users WHERE usrmail='".$formVars["usrmail"]."'".($id > 0 ? " AND usrid != $id" : ""));
      if ($cnt > 0) {
        $form->addError("Účet s tímto emailem už existuje. Pokud jste zapoměli heslo, požádejte si o heslo nové.");
        return;
      }
      $user = new UsersModel();
      if ($id > 0) {
        //editace zaznamu
        
        //zjistim jestli nebyl zmenen email
        $mailNewCode = false;
        $userRow = $user->load($id);
        if (isset($formVars["usrmail"]) && $formVars["usrmail"] != $userRow->usrmail) {
          $formVars["usrmailvcode"] =  $this->getVerifyCode();
          $formVars["usrmailverified"] =  false;
          $mailNewCode = true;
        }
        
        //kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if ($formVars["usrpassw_old"] != "") {
          if (md5($formVars["usrpassw_old"]) != $userRow["usrpassw"]) {
            $this->flashMessage("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno");
            unset($formVars["usrpassw"]);
          } else {
            $formVars["usrpassw"] = md5($formVars["usrpassw"]);  
            $passw_changed = true;
          }
        } else {
          unset($formVars["usrpassw"]);
        }

        unset($formVars["usrpassw_old"]);
        unset($formVars["usrpassw2"]);
        unset($formVars["stadr"]);
        
        $user->update($id, $formVars);
          
        /*
        if ($mailNewCode) $userRow = $user->load($id);
        
        if ($mailNewCode) {
          //odmailuju novy kod
          $this->template->userRow = $userRow;
          $this->template->setFile(APP_DIR.'/../templates/Mails/mailUserMailChanged.phtml');
          
          if ($this->mailSend($userRow->usrname.' <'.$userRow->usrmail.'>', "Ověření emailu", $this->template)) {
            $this->flashMessage('Na nový email byl odeslán ověřovací kód');
          }
        }
        */  
        $this->flashMessage('Vaše údaje byly aktualizovány.');
        if ($passw_changed) {
          $this->flashMessage('Heslo bylo změněno, prosím znovu se přihlašte.');
          $this->redirect('logout');
        } else {     
          $this->redirect('edit');
        }  
      } else {
        //novy zazanam
        $formVars = $form->getValues();
        $formVars["usrdiscount"] = $this->config["DEFAULT_DISCOUT"];
        $mailTemplate = $this->createTemplate();
        //ulozim si do sablony hesle nez se zaheshuje
        $mailTemplate->usrpassw = $formVars["usrpassw"];
        
        //uklidim promenne ktere nejsou v objektu user
        unset($formVars["usrpassw2"]);
        unset($formVars["antispam"]);
        
        //naplnim overovaci kody
        $formVars["usrmailvcode"] = $this->getVerifyCode(); 
        $formVars["usrpassw"] = md5($formVars["usrpassw"]); 
        
        //ulozim novou registraci
        $id = $user->insert($formVars);
        
        if ($id > 0) {
          //naplnim row
          $userRow = $user->load($id);     
          
          $mailTemplate->userRow = $userRow;
          $mailTemplate->setFile(APP_DIR.'/../templates/Mails/mailUserMailAdd.phtml');
          
          $this->mailSend($userRow->usrmail, $this->translator->translate("Registrace"), $mailTemplate);
          
          //prihlasim
          //$this->user->setExpiration('+ 14 days');
          $this->user->login($form['usrmail']->getValue(), $form['usrpassw']->getValue(), self::LOGIN_NAMESPACE);
        }  
        $this->flashMessage('Vaše registrace byla přijata a nyní jste přihlášen/a na svůj účet. Na Váš email jsme Vam poslali přihlašovací údaje.');
        $this->redirect('User:default');
      }
    }
  }
    
  /********************* facilities *********************/
  
  protected function createComponentSendPasswordForm() {
    //prihlasovaci form  
    $form = $this->createAppForm();
    $form->addText('usrmail', 'Email:')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).')
      ->addRule(NForm::EMAIL, 'Email nemá správný formát'); 
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"])
      ->setOption('description', 'test proti robotum')
      ->setHtmlId('antispam')
      ->addRule(NForm::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);  
    $form->addSubmit('submit', 'Zaslat heslo')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'sendPasswordFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
  
     
  /** 
  * editace, registrace novych zakazniku
  */
  protected function createComponentUserAddForm() {      
    $user = new UsersModel();
    $form = $this->createAppForm();
    //$form->setTranslator($this->translator);
    
    //prihlasovaci udaje
    $form->addText('usrmail', 'Váš email', 30)
      ->setEmptyValue('vas@email')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::EMAIL, 'Email nemá správný formát');
    $form->addPassword('usrpassw', 'Heslo', 20)
      ->addRule(NForm::FILLED, 'Prosím vyplňte heslo.')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);      
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addRule(NForm::FILLED, 'Prosím vyplňte heslo podruhé.')
      ->addCondition(NForm::FILLED) 
        ->addRule(NForm::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"]);
    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách")
      ->setDefaultValue(True);
    $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
      ->setHtmlId('antispam')
      ->addRule(NForm::EQUAL, 'Vyplňte číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);    
    $form->addSubmit('save', 'Zaregistrovat se')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'userFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form; 
  }
  
  protected function createComponentUserEditForm() {
    $user = new UsersModel();
    $enums = new EnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $form = $this->createAppForm();
    
    //prihlasovaci udaje
    $form->addGroup('Přihlašovací údaje');
    $form->addText('usrmail', 'Email', 30)
      ->setOption('description', ' slouží zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(NForm::FILLED, 'Prosím vyplňte Váš email.')
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::EMAIL, 'Email nemá správný formát');
    $form->addCheckbox("usrmaillist", "Chci dostávat informace o novinkách a akčních nabídkách");    
        
    //Adresa dodani
    $form->addGroup('Fakturační adresa');
    
    $form->addText('usriname', 'Jméno', 30);
    
    $form->addText('usrilname', 'Přijmení', 30);  
      
    $form->addText('usrifirname', 'Název firmy', 30);    
      
    $form->addText('usristreet', 'Ulice', 30);
    
    $form->addText('usristreetno', 'Číslo popisné', 30);  
      
    $form->addText('usricity', 'Město, obec', 30); 
      
    $form->addText('usripostcode', 'PSČ', 6);
      
    $form->addSelect("usricouid", "Země", $enumCountries)
      ->setTranslator(Null)
      ->addCondition(NForm::EQUAL, 1)
        ->toggle("icDic");  
            
    $form->addText('usrtel', 'Telefon', 20);
    
    $form->addText('usric', 'IČ', 15)
      ->setOption('description', ' 10 číslic, bez mezer');
    $form->addText('usrdic', 'DIČ', 15);
         
    //fakturacni adresa
    $form->addGroup('Adresa dodání')
      ->setOption('embedNext', TRUE);

    $form->addCheckbox('stadr', 'Chci vyplnit adresu dodání (vyplňte jen pokud je jiná než adresa fakturační).')
      ->addCondition(NForm::EQUAL, TRUE) 
        ->toggle('sendBox');

    // subgroup
    $form->addGroup()
      ->setOption('container', NHtml::el('div')->id('sendBox'));    
  
    $form->addText('usrstname', 'Jméno', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte jméno"); 
    
    $form->addText('usrstlname', 'Přijmení', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte příjmení");     
    
    $form->addText('usrstfirname', 'Název firmy', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte název firmy");         
        
    $form->addText('usrststreet', 'Ulice', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte ulici");
    
    $form->addText('usrststreetno', 'Číslo popisné', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte číslo popisné.");
        
    $form->addText('usrstcity', 'Město, obec', 30)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte obec/město"); 
        
    $form->addText('usrstpostcode', 'PSČ', 6)
      ->addConditionOn($form["stadr"], NForm::EQUAL, true)
        ->addRule(NForm::FILLED, "Prosím vyplňte PSČ");
    
    $form->addSelect("usrstcouid", "Země", $enumCountries)
      ->setTranslator(Null)
      ->addRule(NForm::FILLED, 'Prosím vyplňte zemi.');                   

    $form->addGroup('Změna hesla');    
    $form->addPassword('usrpassw_old', 'Původní heslo', 20)
      ->addCondition(NForm::FILLED)
        ->addRule(NForm::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);      
    $form->addPassword('usrpassw', 'Nové heslo', 20)
      ->addConditionOn($form["usrpassw_old"], NForm::FILLED)
        ->addRule(NForm::FILLED, 'Prosím vyplňte nové heslo.')
        ->addRule(NForm::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
    $form->addPassword('usrpassw2', 'Heslo podruhé', 20)
      ->addConditionOn($form["usrpassw_old"], NForm::FILLED)
        ->addRule(NForm::FILLED, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["usrpassw"])
        ->addRule(NForm::MIN_LENGTH, 'Vyplňte prosím minimálně %d znaků', 6);
            
    $form->addGroup();
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'userFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;  
  }
  
  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }
}