<?php

final class Front_SearchPresenter extends Front_BasePresenter {
    
  /** @persistent */
  public $name = '';
  
  /** @persistent */
  public $fulltext = '';
  
  /** @persistent */
  public $man = '';
  
  public function renderDefault () {   
    $this->name = $this->getParam("name");
    $this->fulltext = $this->getParam("fulltext");
    $this->man = $this->getParam("man");
    
    $where  = $this->getWhere();
    
    $product = new ProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $dataSource = $product->getDataSource($product->getSqlList($where));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
  }
  
  protected function createComponentDetailSearchForm() { 
    $form = $this->createAppForm();
    $form->addtext("name", "Název", 10);
    if (!empty($this->name)) $form["name"]->setDefaultValue($this->name);
    
    $form->addtext("fulltext", "Fulltext", 20);
    if (!empty($this->fulltext)) $form["fulltext"]->setDefaultValue($this->fulltext);
    
    $where  = $this->getWhere(true);
    $mans = dibi::query("SELECT manid, manname FROM products  INNER JOIN manufacturers ON (manid=promanid) WHERE $where GROUP BY manid")->fetchPairs('manid', 'manname');
    
    $form->addSelect("man", "Výrobce", $mans)
      ->setPrompt("Nevybrán");
    if (!empty($this->man)) $form["man"]->setDefaultValue($this->man);
    
    $form->addSubmit('detailSearch', 'Hledat')->getControlPrototype()->class('button');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }
  
  public function searchFormSubmitted (NAppForm $form) {
    
    if ($form->isSubmitted()) {
      $vals = $form->getValues();  
    
      $searchParam["name"] = $vals["name"];
      $searchParam["fulltext"] = $vals["fulltext"];
      $searchParam["man"] = $vals["man"];
    
      $this->redirect('Search:default', $searchParam); 
    }
  }
  
  Private function getWhere($manOff = false) {
    $where = " prostatus=0 ";
    if (!empty($this->name)) {
      $where .= ($where != "" ? " AND " : "") . " proname LIKE '%".$this->name."%'";
    }
    if (!empty($this->fulltext)) {
      $where .= ($where != "" ? " AND " : "")."(proname LIKE '%".$this->fulltext."%' OR prodescs LIKE '%".$this->fulltext."%')";  
    } 
    if (!empty($this->man) && $manOff == false) {
      $where .= ($where != "" ? " AND " : "")."promanid=$this->man";  
    }
    
    if ($where == "") $where = "proid=-1";
    return $where;  
  }
  
  function createComponentPaginator($name){
    $vp = new VisualPaginator($this, $name);
  }    
}