<?php

final class Front_ExportPresenter extends Front_BasePresenter {

  public $backlink = '';
  
  const FORMAT_ZBOZICZ = 'zbozicz';
  const FORMAT_JYXOCZ = 'jyxocz';
  const FORMAT_HEUREKACZ = 'heurekacz';
  const FORMAT_GOOGLECOM = 'googlecom';
  
  public function actionProducts($id) {    
    $products = new ProductsModel();
    $where = "";
    switch ($id) {
      case self::FORMAT_HEUREKACZ:
      case self::FORMAT_ZBOZICZ:
      case self::FORMAT_JYXOCZ:
        $where = " AND pronames!='0' ";
        break;
      case self::FORMAT_GOOGLECOM:
        $where = "
         AND CHAR_LENGTH(if(coalesce(pronames,'')='', proname, pronames))>14 
         AND CHAR_LENGTH(prodescs)>14 
         AND progoogleoff=0 ";
        break;
    }
    
    $sql = "SELECT products.*, manufacturers.manname, catpath, catpathids, IF(provatid=0, ".$this->config["VATTYPE_0"].", ".$this->config["VATTYPE_1"].") AS provat 
FROM products 
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
LEFT JOIN manufacturers ON (promanid=manid)
WHERE 
propricea > 0 AND catstatus=0 AND prostatus=0 AND
NOT EXISTS (
    SELECT capproid
    FROM catplaces
        INNER JOIN catalogs on (capcatid=catid) WHERE capproid=proid AND
                                  (catpathids LIKE '|826|%' OR catpathids LIKE '|30|%' OR catpathids LIKE '|285|%' OR catpathids LIKE '|279|%'))
$where
GROUP BY proid";
    
    $this->template->rows=dibi::fetchAll($sql);
    if ($id == self::FORMAT_GOOGLECOM) $this->template->rootCats=dibi::query("SELECT catid,catgooglecat  FROM catalogs WHERE catmasid=0")->fetchPairs("catid", "catgooglecat");
    $this->setView($id);   
  }
  
  public function actionSitemap() {    
    $this->template->products=dibi::fetchAll("SELECT products.*, coalesce(prodateu, prodatec) AS moddate FROM products 
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
WHERE propricea > 0 AND catstatus=0 AND prostatus=0
GROUP BY proid");
    $catalogs = dibi::query("
    SELECT *, coalesce(catdateu, catdatec) AS moddate 
    FROM catalogs 
    WHERE catstatus=0 AND catmasid=0 
    ORDER BY catorder")->fetchAssoc('catid'); 
    foreach ($catalogs as $key => $row) {
      $catalogs[$key]->subCats = dibi::fetchAll("
    SELECT catalogs.*, coalesce(catdateu, catdatec) AS moddate FROM catalogs 
    WHERE catstatus=0 AND catpathids LIKE '|$row->catid|%' AND catlevel=2
    GROUP BY catid");
    }
    $this->template->catalogs = $catalogs; 
    $this->template->menuTopExport = dibi::fetchAll("SELECT *, coalesce(pagdateu, pagdatec) AS moddate FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
    $this->template->menuIndexs = dibi::fetchAll("
    SELECT *, coalesce(pagdateu, pagdatec) AS moddate FROM menuindexs 
    LEFT JOIN pages ON (pagid=meipagid) 
    WHERE meimasid=0 AND meistatus=0 AND meipagid > 0 ORDER BY meiorder");
    
  }  
}