<?php
abstract class Helpers {
  
  private $config;
  
  public static function loader($helper) {
  
    $callback = callback(__CLASS__, $helper);
    if ($callback->isCallable()) {
        return $callback;
    }
  }
  
  public static function formatPriceVat($row, $decimals=Null) {
    if ((double) $row->proprice == 0) return "na dotaz";
    $cache = NEnvironment::getCache('app');
    $config = $cache["config"];
    //zjistim sazbu DPH
    $vat = (int)$config["VATTYPE_".$row->provatid];
    $price = $row->proprice*(1+($vat/100));
    return self::formatPrice($price, $decimals)." s DPH";
  }

  public static function countPriceVat($row, $decimals=Null) {
    if ((double) $row->proprice == 0) return 0;
    $cache = NEnvironment::getCache('app');
    $config = $cache["config"];
    //zjistim sazbu DPH
    $vat = (int)$config["VATTYPE_".$row->provatid];
    $price = (double)$row->proprice*(1+($vat/100));
    if ($decimals == Null) $decimals = (int)$config["PRICE_ROUNDPOS"];
    return round($price, $decimals);
  }
  
  public static function formatPrice($price, $decimals=Null) {
    if ((double)$price == 0) return "na dotaz";
    if ($decimals == Null) {
      $cache = NEnvironment::getCache('app');
      $config = $cache["config"];
      $decimals = (int)$config["PRICE_ROUNDPOS"];
    }  
    return str_replace(" ", "\xc2\xa0", number_format($price, $decimals, ",", " "))." Kč";
  }
  
  /**
  * vraci nazev obrazku
  * 
  * @param string $path [product/list,product/detail,product/big,catalog]
  * @param dibirow $row
  * 
  * @return string cesta k obrazku
  */
  public static function getPicName($row, $path) {
    $pathArr = explode('/', $path);
    $path = "pic/$path";
    $picPath = WWW_DIR."/$path/";
    $fileName = "";
    
    switch ($pathArr[0]) {
       case 'product':
         $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
         break;
       case 'catalog':
         $fileName = $row->catid.'.jpg'; 
         break;
    }
    if (!file_exists($picPath.$fileName)) $fileName = "empty.jpg";
    $fileName = rawurlencode($fileName);
    return($path."/".$fileName);
  }
  
  public static function getProPicSrcSize($row, $size, $baseUri) {
    $path = "pic/product/$size";
    $picPath = WWW_DIR."/$path/";
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
    $cache = NEnvironment::getCache('app');
    $config = $cache["config"];
    
    if (file_exists($picPath.$fileName)) {
      //$image = NImage::fromFile($picPath.$fileName);
      //$w = $image->getWidth();
      //$h = $image->getHeight();  
      $size = explode('x', $config["PROPICSIZE_".strtoupper($size)]);
      $w = $size[0];             
      $h = $size[1];
    } else {
      $cache = NEnvironment::getCache('app');
      $config = $cache['config'];
      $size = explode('x', $config["PROPICSIZE_".strtoupper($size)]);
      $w = $size[0];             
      $h = $size[1];
      $fileName = "empty.jpg";
    }
     
    $picPath = $baseUri."/".$path."/".$fileName;
    return(' src="'.$picPath.'" width="'.$w.'" height="'.$h.'" '); 
  }
  
  /**
  * vraci URL klic zbozi
  * 
  * @param dibirow $row 
  * @return URL klic
  */
  public static function getProKey($row) {
    return((!empty($row->prokey) ? $row->prokey : NStrings::webalize($row->proname)));
  }
  
  /**
  * vraci URL klic katalogu
  * 
  * @param dibirow $row 
  * @return URL klic
  */
  public static function getURLKey($name, $key) {
    return((!empty($key) ? $key : NStrings::webalize($name)));
  }
  
  public static function getMP3FileName($audio) {
    $fName = "";
    $audio = trim($audio);
    $arr = array();
    $arr2 = array();
    if (!empty($audio)) {
      $arr = explode("\n", $audio);  
    }
    foreach ($arr as $value) {
      $arr2 = explode(";", $value);
      if (!empty($arr2[2])) {
        $arr2[2] = trim($arr2[2]);
        if (substr($arr2[2], -3) == 'mp3') {
          $fName = $arr2[2];  
          break;
        }  
      }  
    }
    return($fName);
  }
  
  
}  
?>