{default $pageTitle=$template->translate('<PERSON>nta<PERSON><PERSON><PERSON>, ad<PERSON><PERSON>')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
  <h3>{$pageTitle}</h3>
  {form orderContactForm}
  {* vyk<PERSON>leni chyb pokud ma vypnuty JS *}
  <ul class="errors" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$error}</li>
  </ul>
  <div class="adresa">
  <fieldset class="border">
    <legend>{_'Fakturační a současně doručovací adresa'}</legend>
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['ordiname']->getLabel()->class('required') ?><br />
        <?php echo $form['ordiname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordilname']->getLabel()->class('required') ?><br />
        <?php echo $form['ordilname']->control->cols(30) ?></td>
      </tr>
      {if $presenter->lang == 'cs'}
      <tr>
        <td><?php echo $form['ordifirname']->getLabel() ?> <br />
        <?php echo $form['ordifirname']->control->cols(30) ?></td>
      </tr>
      {/if}
      <tr>
        <td><?php echo $form['ordistreet']->getLabel()->class('required') ?><br />
        <?php echo $form['ordistreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordistreetno']->getLabel()->class('required') ?><br />
        <?php echo $form['ordistreetno']->control->cols(30) ?></td>
      </tr>        
      <tr>
        <td><?php echo $form['ordicity']->getLabel()->class('required') ?><br />
        <?php echo $form['ordicity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordipostcode']->getLabel()->class('required') ?><br />
        <?php echo $form['ordipostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td>{label ordicouid 'class'=>'required'}{_'Země'}{/label}<br />
        {input ordicouid 'cols'=>30, 'class'=>'submitForm'}</td>
      </tr>
      <tr>
        <td><?php echo $form['ordmail']->getLabel()->class('required') ?><br />
        <?php echo $form['ordmail']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordtel']->getLabel()->class('required') ?><br />
        <?php echo $form['ordtel']->control->cols(30) ?></td>
      </tr>
      </tbody>
    </table>
    {if $lang != 'en'}  
    <table id="icDic">  
      <tbody>
      <tr>
        <td><?php echo $form['ordic']->getLabel() ?><br />
        <?php echo $form['ordic']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['orddic']->getLabel() ?><br />
       <?php echo $form['orddic']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['dicempty']->control ?><?php echo $form['dicempty']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>
    {/if}
  </fieldset>
  {if $lang != 'en'} 
  <fieldset class="border">
    <legend>{_'Adresa dodání'}</legend>
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['shipto']->control ?><?php echo $form['shipto']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>  
    <div id="sendBox">
    <table>
      <tbody>
      <tr>
        <td>{label ordstname /}<br />
        <?php echo $form['ordstname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstlname']->getLabel() ?> <br />
        <?php echo $form['ordstlname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstfirname']->getLabel() ?> <br />
        <?php echo $form['ordstfirname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordststreet']->getLabel() ?> <br />
        <?php echo $form['ordststreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordststreetno']->getLabel() ?> <br />
        <?php echo $form['ordststreetno']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordstcity']->getLabel() ?> <br />
       <?php echo $form['ordstcity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstpostcode']->getLabel() ?> <br />
       <?php echo $form['ordstpostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstcouid']->getLabel() ?> <br />
       {input ordstcouid 'cols'=>30, 'class'=>'submitForm'}</td>
      </tr>
      </tbody>
  </table>
  </div>
  </fieldset>
  {/if}
  <fieldset class="border">
    <legend>{_'Vzkaz k objednávce'}</legend>
  <table>
      <tbody>
      <tr>
        <td><?php echo $form['ordnote']->control->cols(60)->rows(3) ?></td>
      </tr>
      </tbody>
  </table>
  <table>
    <tbody>
      {if !$identity->isLoggedIn()} 
      <tr>
        <td><?php echo $form['antispam']->getLabel() ?><br />
        <?php echo $form['antispam']->control->cols(15) ?><small>{_'test proti robotum'}</small></td>
      </tr>
      {/if}
    </tbody>
  </table>
  </fieldset>
  <div class="submit"><a href="{plink 'default'}">{_'Zpět do košíku'}</a>  {input submit 'class'=>'button'} </div>
  {/form orderContactForm}
 </div>
  {if !$identity->isLoggedIn()} 
  <script>
  $('#antispam').val('{!$presenter->config["ANTISPAM_NO"]}').closest('tr').hide();  
  </script>
  {/if}
{/block}