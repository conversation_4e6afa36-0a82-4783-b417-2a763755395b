<?php $template->registerHelperLoader('Helpers::loader'); ?>
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<SHOP>
{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
<PRODUCT>{if !empty($row->pronames)}{$row->pronames}{else}{$row->proname}{/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
<IMGURL>{$baseUri}/{!$template->getPicName($row, 'product/detail')}</IMGURL>
<?php 
$catPath = str_replace('|', ' | ', $row->catpath); 
$priceVat = round((1+((int)$row->provat/100)) * $row->propricea, 0);
?>
<PRICE_VAT>{$priceVat}</PRICE_VAT>
<CATEGORYTEXT>{$catPath}</CATEGORYTEXT>
</SHOPITEM>
{/foreach}
</SHOP>