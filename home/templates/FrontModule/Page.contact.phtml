{default pageTitle       => (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{default pageKeywords    => $page->pagkeywords}
{default pageDescription => $page->pagdescription}

{block #content}
{if $menuIndex}
<a id="gotoeshop" href="
{if $menuIndex->meicatid > 0}
{plink Catalog:detail $menuIndex->catid, $template->getURLKey($menuIndex->catname, $menuIndex->catkey)}
{elseif !empty($menuIndex->meiprocode)}
{plink Product:detail $menuIndex->proid, $menuIndex->prokeymaster, $template->getProKey($menuIndex)}
{/if}
">Vstoupit do eshopu</a>
{/if}
{!$page->pagbody}
<h4>{_'Kontaktní formulář'}</h4>
<div id="contactform">
{form contactForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$template->translate($error)}</li>
  </ul>
  <table>
    <tbody>
      <tr>
        <th><?php echo $form['conname']->getLabel()->class('required') ?>:</th>
        <td><?php echo $form['conname']->getControl() ?></td>
      </tr><tr>  
        <th><?php echo $form['conmail']->getLabel()->class('required') ?>: </th>
        <td><?php echo $form['conmail']->getControl() ?> </td>
      </tr><tr>    
        <th><?php echo $form['congsm']->getLabel() ?>:</th>
        <td><?php echo $form['congsm']->getControl() ?> </td>
      </tr><tr>    
        {* antispamova kontrola *}
      </tr><tr>  
        <th><?php echo $form['antispam']->getLabel() ?>:</th>
        <td><?php echo $form['antispam']->getControl() ?></td>
      </tr><tr>    
        <th><?php echo $form['connote']->getLabel() ?></th>
        <td><?php echo $form['connote']->getControl() ?></td> 
      </tr><tr>    
        <th>&nbsp;</th>
        <td><?php echo $form['save']->getControl() ?></td> 
      </tr>
    </tbody>
  </table>
{/form}
</div>
<script type="text/javascript">
$('#antispam').val('{!$presenter->config["ANTISPAM_NO"]}').closest('tr').hide();
</script>
{/block}
