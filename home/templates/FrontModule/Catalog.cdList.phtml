{* Catalog.detail.phtml *}

{* nastaveni promennych *}
{default pageTitle       => 'Spirituální a léčivá hudba, Seznam CD'}
{default pageKeywords    => 'Spirituální a léčivá hudba, Seznam CD'}
{default pageDescription => 'Spirituální a léčivá hudba, Seznam CD'}

{block #content}

  <h3> {$pageTitle} </h3>
  <p>{_'Zde je uveden úplný seznam všech dostupných CD nahrávek s technologií Hemi-Sync® včetně katalogových čísel. Ve výchozím stavu jsou řazeny abecedně, ale kliknutím na odkazy níže si můžete nechat vybrat pouze Vámi zvolený typ Human Plus®, Mind Food®, Metamusic®, Gateway Experience® nebo CD Série.'}</p>
    
  {* vypis hlavnich kategorii katalogu bez zarazeni dle pouziti *}
  <ul>
  {foreach $menuCatalog as $catalog}
    {if  $catid != $catalog->catid}
    <li><a href="{plink Catalog:cdList, $catalog->catid}">{_'Vyber všechny CD'} {$catalog->catname}</a></li>
    {else}
    <li><strong>{$catalog->catname}</strong></li>
    {/if}
  {/foreach}
  {if  $catid > 0}
    <li><a href="{plink Catalog:cdList, 0}">{_'Vyber všechny CD'}</a></li>
    {else}
    <li><strong>{_'Vyber všechny CD'}</strong></li>
    {/if}
  </ul>
    
  {* vypis polozek zbozi *}
  <table id="productlist">
  {foreach $productsData as $row}
  <tr {if $iterator->isEven()} class="even" {/if}>
  <td><a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{$row->proname}</a></td>
  <td>{$row->catname} - {$row->procode}</td>  
  </tr>
  {/foreach}
  </table>
{/block}