{default pageTitle       => $template->translate('<PERSON><PERSON><PERSON>')}
{default pageRobots      => "nofollow,noindex"}

<?php
  $GLOBALS["ecommProId"] = $basket->proids;
  $GLOBALS["ecommPageType"] = 'cart';
  $GLOBALS["ecommTotalValue"] = round($basket->priceSumTotalVat, (int)$config["PRICE_ROUNDPOS"]);
?>

{block #content}
{if $presenter->getParam('ok') != 1}
<h3>{_'Obsah Vašeho košíku'}</h3>
  {if count($basket->items) > 0} 
{form basketForm}
<?php $form->render('errors') ?> 
 <div class="table basket">
 <table class="grid">
 <thead>
    <tr>
      <th class="name">{_'Název'}</th>
      <th>{_'Kusy'}</th>
      <th>{_'Cena/kus bez DPH'}</th>
      <th>{_'Cena bez DPH'}</th>
      <th>{_'DPH'}</th>
      <th>{_'Smazat'}</th>
    </tr>
  </thead>
  <tfoot>
    <tr>
      <td class="name" colspan="3">{_'Cena za zboží'} bez DPH</td>
      <td>{$basket->priceSumTotal|formatPrice}</td>
      <td></td>
      <td></td>
    </tr>
    <tr>
      <td class="name" colspan="3">{_'Cena za zboží'} s DPH</td>
      <td>{$basket->priceSumTotalVat|formatPrice}</td>
      <td></td>
      <td></td>
    </tr>
  </tfoot>
  <tbody>
  {foreach $basket->items as $id=>$value}
    <tr>
      <td class="name"><a href="{plink Product:detail, $productRows[$id]->proid, $productRows[$id]->prokeymaster,  $template->getProKey($productRows[$id])}">{$productRows[$id]->proname}</td>
      <td><?php echo $form['count_'.$productRows[$id]->proid]->getControl() ?></td>
      <td>{$productRows[$id]->proprice|formatPrice}</td>
      <td>{($productRows[$id]->proprice*$value)|formatPrice}</td>
      <td>{$presenter->config["VATTYPE_".$productRows[$id]->provatid]}%</td>
      <td><a href="{plink Basket:delete, $productRows[$id]->proid}" onclick="return DeleteConfirmFront('{!_'Opravdu chcete smazat položku'} {!$productRows[$id]->proname}');" title="{_'Odstranit z nákupního košíku'}"><img src="{$baseUri}/ico/delete.png" width="16" height="16" alt="{_'Odstranit z nákupního košíku'}" /></a></td>
    </tr>
  {/foreach}
    {if $basket->discountVal > 0}
    <tr class="background">
      <td colspan="3" class="name">{_'Sleva'} {$basket->discountPer}%</td>
      <td>{$basket->discountVal|formatPrice}</td>
      <td></td>
      <td></td>
    </tr>
    {/if}
    {if $basket->weightSum > 0}
    <tr class="background">
      <td colspan="3" class="name">{_'Hmotnost objednávky'}: {$basket->weightSum|number:2:',':' '}&nbsp;Kg</td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    {/if}
  </tbody>
  </table>
  </div>
<div class="submit"> <a href="javascript:history.go(-1)">{_'Pokračovat v nákupu'}</a>  <?php echo $form['recalc']->getControl() ?>
<?php echo $form['makeorder']->getControl() ?></div>
{/form}
  {* vypis slev, dopravy zdarma *}
  {if $basket->weightSum > $presenter->config["WEIGHT_LIMIT"]}
  <p><strong>Vaše objednávka přesahuje váhový limit</strong>, pro odeslání běžnou přepravní službou. Cena za dopravu bude stanovena po odeslání objednávky na základě <a href="{plink Page:detail 'doprava-platba'}">platného ceníku pro dodávku zboží</a>.</p>
  {/if}
  {if $nextDisc}
  <p>Pokud ještě objednáte za <strong>{$nextDisc->diff|formatPrice}</strong> získáte <strong>slevu {$nextDisc->dispercent}%</strong> na celý nákup.</p>
  {/if}
  {*
  {foreach $delFree as $row}
    {if $iterator->isFirst()}
    <p>
    {/if}
    K tomuto nákupu získáváte dopravu {$row['mas']->delname} {$row->delname} <strong>ZDARMA!</strong>
    {if $iterator->isLast()}
    </p>
    {else}
    <br />
    {/if}
  {/foreach}
  {foreach $nextDelFree as $row}
    {if $iterator->isFirst()}
    <p>
    {/if}
    Pokud objednáte ještě za <strong>{$row->diff|formatPrice}</strong>, získáte dopravu {$row['mas']->delname} {$row->delname} <strong>ZDARMA!</strong>
    {if $iterator->isLast()}
    </p>
    {else}
    <br /> 
    {/if}
  {/foreach}
  *}
  {else}
  <p>{_'Košík je prázdný'}</p>
  {/if}  
  
  {if $userRow->usrid == 0} 
  {!$blockPromoRegistrace->pagbody}
  {/if}
{else}

{/if}  
{/block}
