{* Search.default.phtml *}

{* nastaveni promennych *}
{default pageTitle       => $template->translate('Vyhledávání')}
{default pageKeywords    => ''}
{default pageDescription => $template->translate('Vyhledávání zboží')}

<?php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'searchresults';
  $GLOBALS["ecommTotalValue"] = 0;
?>

{block #content}
  <?php
  $proIds = array();
  ?>

  <h3>{_'Vyhledávání'}</h3>
  <div id="searchForm">
  {control detailSearchForm}
  </div>
  <br />
  {* vypis polozek zbozi *}
  <div id="kat">
<div id="katin">
  {foreach $productsData as $row}
  <?php
    $proIds[] = $row->proid;
    $GLOBALS["ecommTotalValue"] += $template->countPriceVat($row);
  ?>
  
  <div class="kat">
  <div class="in">
    <h4><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}">{$row->proname|truncate:70}</a></h4>
    <span><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}"><img {!$template->getProPicSrcSize($row, 'list', $baseUri)} alt="{$row->proname} - {_'zobrazit detail zboží'}" /></a></span>
    <p>{!$row->prodescs|striptags|truncate:60}</p>
    <p class="price"> <strong>{_'Cena'}: {$row->proprice|formatPrice}</strong> <a class="adbasket" href="{plink Basket:add, $row->proid}" title="{_'Přidat do košíku'}">{_'Do košíku'}</a> <a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}" class="adetail">{_'Detail'}</a></p>
    <hr />
  </div> 
  </div>
  {if $iterator->counter % 4 == 0}
  <div class="end"><hr></div>
  {/if}
  {/foreach}
 </div>
</div> 
  {* strankovani *}
  {control paginator}
  <?php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  ?>
{/block}