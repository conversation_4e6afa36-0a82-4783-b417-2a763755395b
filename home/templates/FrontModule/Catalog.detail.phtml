{* Catalog.detail.phtml *}

{* nastaveni promennych *}
{default pageTitle       => $catalogData->catname}
{default pageKeywords    => $catalogData->catkeywords}
{default pageDescription => empty($catalogData->catdescription) ? $template->striptags($catalogData->catdesc) : $catalogData->catdescription}

<?php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'category';
  $GLOBALS["ecommTotalValue"] = 0;
?>

{block #content}
  <?php
  $proIds = array();
  ?>
  
  <p>{include #catalogPath, separ => "\xc2\xa0> "}</p>
  <h3> {$catalogData->catname} </h3>
  <?php
    $fileName = $catalogData->catid.'.jpg';
    $filePath = WWW_DIR."/pic/catalog/";
    if (!file_exists($filePath.$fileName)) $fileName = "";
  ?>
  <p class="catimg">{if !empty($fileName)}<img src="{$baseUri}/pic/catalog/{$fileName}" />{/if} {!$catalogData->catdesc}</p>
  {* podrizene kategorie *}
  {foreach $catalogSubItemsData as $item}
  {if $iterator->isFirst()}
    <p>{_'Podkategorie'}: 
  {/if}
  <a href="{plink Catalog:detail, $item->catid, $item->catkeymaster, $template->getURLKey($item->catname, $item->catkey)}">{$item->catname}</a>
  {if $iterator->isLast()}
  </p>
  {else}
   | 
  {/if}
  {/foreach}
  
  {* vypis polozek zbozi *}
<!--catalog - new -->
<?php
   $presenter->productsRows = $productsData;
 ?>
 {form addBasketForm}
  <div id="kat">
<div id="katin">
  {if count($productsData) > 0}
  <div id="orderby">
  <?php
     $ss = (string)$presenter->getParam('s');
     $us_price_val = 'price';
     $us_price_text = 'Cena od nejlevnějšího';
     $us_name_val = 'name';
     $us_name_text = 'Název A-Z';
     $us_default_val = '';
     $us_default_text = 'Výchozí';
     $selPrice = False;
     $selName = False;
     $seldefault = True;
     if (!empty($ss)) {
       switch ($ss) {
          case '':
            $us_default_val = '';
            $us_default_text = 'Výchozí';
            $seldefault = True;
            break;
          case 'price':
            $us_price_val = 'price_';
            $us_price_text = 'Cena od nejlevnějšího';
            $selPrice = True;
            $seldefault = false;
            break;
          case 'name':
            $us_name_val = 'name_';
            $us_name_text = 'Název A-Z';
            $selName = True;
            $seldefault = false;
            break;
          case 'price_':
            $us_price_val = 'price';
            $us_price_text = 'Cena od nejdražšího';
            $selPrice = True;
            $seldefault = false;
            break;
          case 'name_':
            $us_name_val = 'name';
            $us_name_text = 'Název Z-A';
            $selName = True;
            $seldefault = false;
            break;  
       }
     }
   ?>
  <div><span>Řazení:</span> <a {if $seldefault}class="selected"{/if} href="{plink 'this', 's'=>$us_default_val}">{$us_default_text}</a> <a {if $selName}class="selected"{/if} href="{plink 'this', 's'=>$us_name_val}">{$us_name_text}</a> <a {if $selPrice}class="selected"{/if} href="{plink 'this', 's'=>$us_price_val}">{$us_price_text}</a></div>
  <br />
  </div>
  {/if}
  {foreach $productsData as $row}
  <?php
    $proIds[] = $row->proid;
    $GLOBALS["ecommTotalValue"] += $template->countPriceVat($row);
  ?>
  <div class="kat">
  <div class="in">
    <h4 class="none"><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}">{$row->proname|truncate:70}</a></h4>
    <span><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}"><img {!$template->getProPicSrcSize($row, 'list', $baseUri)} alt="{$row->proname} - {_'zobrazit detail zboží'}" /></a></span>
    <p>{!$row->prodescs|striptags|truncate:60}</p>
    <p class="price"> <strong>{$row->proprice|formatPrice}</strong> {if $row->proprice > 0}<?php echo $form[$row->proid]['add_'.$row->proid]->control ?> <?php echo $form[$row->proid]['qty']->control ?>{/if}<a href="{plink Product:detail, $row->proid,  $row->prokeymaster, $template->getProKey($row)}" class="adetail">{_'Detail'}</a></p>
    <hr />
  </div> 
  </div>
  {if $iterator->counter % 4 == 0}
  <div class="end"><hr></div>
  {/if}
  {/foreach}
 </div>
</div> 
{/form}
<!--catalog - new -->
  {* strankovani *}
  {control paginator}
  <?php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  ?>
{/block}

{* vypise drobecky pro prislusnou kategorii katalogu *}
{block #catalogPath}
  <?php 
  $arrid = explode("|", trim($catalogData->catpathids, '|'));
  $arrname = explode("|", trim($catalogData->catpath, '|')); 
  ?>
    {foreach $arrid as $id}
      {if ($id == $catalogData->catid)} 
  <strong>{$arrname[$iterator->getCounter()-1]}</strong>
      {else}
      
  <a href="{plink Catalog:detail, $id}">{$arrname[$iterator->getCounter()-1]}</a>{$separ}       
      
      {/if}
    {/foreach}
    
{/block}
