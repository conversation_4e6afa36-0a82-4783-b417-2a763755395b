{default $pageTitle=$template->translate('Způsob dodání a odeslání')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
<h3>{$pageTitle}</h3>

{form orderDelModeForm}
  {* vyk<PERSON>leni chyb pokud ma vypnuty JS *}
  <ul class="errors" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$error}</li>
  </ul>
  <h4>{_'Vyberte dopravu a způsob platby'}</h4>
  {if $basket->specDel}
  <p>Vaše objednávka obsahuje zboží, které <strong>nelze odeslat běžnou přepravní službou</strong>. Po odeslání objednávky Vás budeme kontaktovat a dohodneme individuální dopravu.</p>
  {elseif $basket->weightSum > $presenter->config["WEIGHT_LIMIT"]}
  <p>Vaše objednávka obsahuje zboží, které <strong>nelze odeslat běžnou přepravní službou</strong>. Po odeslání objednávky Vás budeme kontaktovat a dohodneme individuální dopravu.</p>
  {/if}
  <table>
    <thead>
      <tr>
        <th>{_'Doprava'}</th>
        <th>{_'Způsob platby'}</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{input delmasid}</td>
        <td>{input orddelid}</td>
      </tr>
    </tbody>
  </table>
  <br />
 <h4>{_'Objednané položky'}</h4>
 <div class="table basket">
 <table class="grid">
 <thead>
    <tr>
      <th class="name">{_'Název'}</th>
      <th>{_'Kusy'}</th>
      <th>{_'Cena/kus bez DPH'}</th>
      <th>{_'Cena bez DPH'}</th>
      <th>{_'DPH'}</th>
    </tr>
  </thead>
  <tfoot>
    <tr>
      <td class="name">Celkem {if $basket->specDel}za zboží {else}včetně dopravy{/if} bez DPH</td>
      <td></td>
      <td></td>
      <td>{$basket->priceSumTotal|formatPrice}</td>
      <td></td>
    </tr>
    <tr>
      <td class="name">Celkem {if $basket->specDel}za zboží {else}včetně dopravy{/if} s DPH</td>
      <td></td>
      <td></td>
      <td>{$basket->priceSumTotalVat|formatPrice}</td>
      <td></td>
    </tr>
  </tfoot>
  <tbody>
  <?php $sum = 0; ?>
  {foreach $basket->items as $id=>$value}
  <?php
    $sum += ($productRows[$id]->proprice*$value);
  ?>
    <tr>
      <td class="name">{$productRows[$id]->proname}</td>
      <td>{$value}</td>
      <td>{$productRows[$id]->proprice|formatPrice}</td>
      <td>{($productRows[$id]->proprice*$value)|formatPrice}</td>
      <td>{$presenter->config["VATTYPE_".$productRows[$id]->provatid]}%</td>
    </tr>
    {*
    {if $productRows[$id]->probigsize || $productRows[$id]->prooffer}
    <tr>
      <td colspan="5"><small>
      {if $productRows[$id]->probigsize}
        Zboží nelze dopravit vybraným druhem dopravy. Náš obchodník  Vás bude kontaktovat s nabídkou možné dopravy.<br />
      {/if}
      {if $productRows[$id]->prooffer}
        Vaše objednávka byla změněna v poptávku. Náš obchodník Vás bude kontaktovat pro upřesnění technických parametrů, nabídne Vám montáž a dohodne další nezbytné požadavky na dodání zboží.
      {/if}
    </small></td></tr>
    {/if}  
    *}
  {/foreach}
  <tr class="background">
      <td class="name"  colspan="3">{_'Cena za zboží'}</td>
      <td>{$sum|formatPrice}</td>
      <td></td>
    </tr>
  {if $basket->discountVal > 0}
    <tr class="background">
      <td class="name"  colspan="3">{_'Sleva'} {$basket->discountPer}%</td>
      <td>{$basket->discountVal|formatPrice}</td>
      <td></td>
    </tr>
  {/if}
  {if $basket->weightSum > 0}
    <tr class="background">
      <td colspan="3" class="name">{_'Hmotnost'}: {$basket->weightSum|number:2:',':' '}&nbsp;Kg</td>
      <td></td>
      <td></td>
    </tr>
    {/if}
  {if $delMode && !$basket->specDel}
    <tr class="background">
      <td class="name" colspan="3">{_'Poštovné a balné'}</td>
      <td>{if $delMode->delprice > 0}{$delMode->delprice|formatPrice}{else}{if $delMode->delmasid==3}bude stanovena{else}ZDARMA{/if}{/if}</td>
      <td>{$presenter->config["VATTYPE_0"]}%</td>
    </tr>
  {elseif $basket->specDel}
    <tr class="background">
      <td class="name" colspan="3">{_'Poštovné a balné'}</td>
      <td>bude stanovena</td>
      <td>{$presenter->config["VATTYPE_0"]}%</td>
    </tr>
  {/if}
  </tbody>
  </table>
  <p><a href="{plink 'default'}">{_'Upravit položky'}</a></p>
  </div>
  <div class="adresa">
  <h4>{_'Fakturační adresa'}</h4>
  <p>
    {$basket->contact->ordifirname}<br />
    {$basket->contact->ordiname} {$basket->contact->ordilname}<br />
    {$basket->contact->ordistreet} {$basket->contact->ordistreetno}<br />
    {$basket->contact->ordipostcode} {$basket->contact->ordicity}<br />
    {$enum_countries[$basket->contact->ordicouid]}
    <br />
    Email: {$basket->contact->ordmail}<br />
    {_'Telefon'}: {$basket->contact->ordtel}<br />
    {if $lang == 'cs'}
    <br />
    IČ: {$basket->contact->ordic}<br />
    DIČ: {$basket->contact->orddic}<br />
    {/if}
  </p>
  {if !empty($basket->contact->ordstlname)}
  <h4>{_'Dodací adresa'}</h4>
  <p>
    {$basket->contact->ordstfirname}<br />
    {$basket->contact->ordstname} {$basket->contact->ordstlname}<br />
    {$basket->contact->ordststreet} {$basket->contact->ordststreetno}<br />
    {$basket->contact->ordstpostcode} {$basket->contact->ordstcity}<br />
    {$enum_countries[$basket->contact->ordstcouid]}<br />
  </p>
  {/if}
  <p><a href="{plink 'orderContact'}">{_'Upravit kontaktní údaje'}</a></p>
  </div>
  {*
  <div class="adresa">
  <fieldset class="border">
    <legend>{_'Nabídka registrace'}</legend>
    <table>
      <tbody>
      <tr>
        <td>
        <?php echo $form['register']->control->cols(30) ?> <?php echo $form['register']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>
    <table id="regForm">
      <tbody>  
      <tr>
        <td><?php echo $form['usrpassw']->getLabel()->class('required') ?><br />
        <?php echo $form['usrpassw']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['usrpassw2']->getLabel()->class('required') ?><br />
        <?php echo $form['usrpassw2']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td>
        <?php echo $form['usrmaillist']->control->cols(30) ?> <?php echo $form['usrmaillist']->getLabel()->class('required') ?></td>
      </tr>
      
    </tbody>
    </table>
  </div>
  *}
  <div class="submit">
    <strong>{_'Odesláním formuláře potvrzujete, souhlas s Obchodními podmínkami.'}</strong><br />
    <a href="{plink default}">{_'Zpět do košíku'}</a> {input makeorder 'class'=>'button'}
  </div>
  {/form}
  <script>
    $("input[name=delmasid]").change(function() {
      window.location.href = {plink orderDelMode}+'?m='+$(this).val();
    });
    $("input[name=orddelid]").change(function() {
      window.location.href = {plink orderDelMode 'm'=>$delmasid}+'&d='+$(this).val();
    });
  </script>
{/block}