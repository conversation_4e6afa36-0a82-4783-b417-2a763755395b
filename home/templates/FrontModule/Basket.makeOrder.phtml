{default $pageTitle=$template->translate('Odeslání objedná<PERSON>ky')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
<h3>{_'Objednané polož<PERSON>'}</h3>
 <div class="table basket">
 <table class="grid">
 <thead>
    <tr>
      <th class="name">{_'Název'}</th>
      <th>{_'Kusy'}</th>
      <th>{_'Cena/kus'}</th>
      <th>{_'Cena'}</th>
    </tr>
  </thead>
  <tfoot>
    <tr>
      <td class="name">{_'Celkem'}</td>
      <td></td>
      <td></td>
      <td>{$basket->priceSumTotal|formatPrice}</td>
    </tr>
  </tfoot>
  <tbody>
  {foreach $basket->items as $id=>$value}
    <tr>
      <td class="name">{$productRows[$id]->proname}</td>
      <td>{$value}</td>
      <td>{$productRows[$id]->proprice|formatPrice}</td>
      <td>{($productRows[$id]->proprice*$value)|formatPrice}</td>
    </tr>
  {/foreach}
  {if $basket->discountVal > 0}
    <tr class="background">
      <td class="name">{_'Sleva'} {$basket->discountPer}%</td>
      <td></td>
      <td></td>
      <td>{$basket->discountVal|formatPrice}</td>
    </tr>
    {/if}
    {if $basket->weightSum > 0}
    <tr class="background">
      <td colspan="3" class="name">{_'Hmotnost objednávky'}</td>
      <td>{$basket->weightSum|number:2:',':' '}&nbsp;Kg</td>
      <td></td>
    </tr>
    {/if}
  </tbody>
  </table>
  </div>
  
  

  {form makeOrderForm}
  {* vykresleni chyb pokud ma vypnuty JS *}
  <ul class="errors" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$error}</li>
  </ul>
  <div class="adresa">
  <fieldset class="border">
    <table>
      <thead>
      <tr>
        <th>{_'Způsob dodání'}</th>
        <th>{_'Způsob platby'}</th>
      </tr>
      </thead>
      <tbody>
      <tr>
        <td>{input delmasid 'class'=>'submitForm'}</td>
        <td>{input orddelid 'class'=>'submitForm'}</td>
      </tr>
      </tbody>
      </table>
  </fieldset><br />  
  <fieldset class="border">
    <legend>{_'Adresa dodání'}</legend>
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['ordstname']->getLabel() ?> <br />
        <?php echo $form['ordstname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstlname']->getLabel() ?> <br />
        <?php echo $form['ordstlname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstfirname']->getLabel() ?> <br />
        <?php echo $form['ordstfirname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordststreet']->getLabel() ?> <br />
        <?php echo $form['ordststreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordststreetno']->getLabel() ?> <br />
        <?php echo $form['ordststreetno']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordstcity']->getLabel() ?> <br />
       <?php echo $form['ordstcity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstpostcode']->getLabel() ?> <br />
       <?php echo $form['ordstpostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstcouid']->getLabel() ?> <br />
       {input ordstcouid 'cols'=>30, 'class'=>'submitForm'}</td>
      </tr>
      <tr>
        <td><?php echo $form['ordmail']->getLabel() ?><br />
        <?php echo $form['ordmail']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordtel']->getLabel() ?><br />
        <?php echo $form['ordtel']->control->cols(30) ?> <small>{_'9 číslic bez mezer'}</small></td>
      </tr>
      <tr>
        <td><?php echo $form['ordic']->getLabel() ?><br />
        <?php echo $form['ordic']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['orddic']->getLabel() ?><br />
       <?php echo $form['orddic']->control->cols(30) ?></td>
      </tr>
      </tbody>
  </table>
  </fieldset>
  <fieldset class="border">
    <legend>{_'Fakturační adresa'}</legend>
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['shipto']->control ?><?php echo $form['shipto']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>  
    <div id="sendBox">
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['ordiname']->getLabel() ?><br />
        <?php echo $form['ordiname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordilname']->getLabel() ?><br />
        <?php echo $form['ordilname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordifirname']->getLabel() ?> <br />
        <?php echo $form['ordifirname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordistreet']->getLabel() ?><br />
        <?php echo $form['ordistreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <td><?php echo $form['ordistreetno']->getLabel() ?><br />
        <?php echo $form['ordistreetno']->control->cols(30) ?></td>
      </tr>        
      <tr>
        <td><?php echo $form['ordicity']->getLabel() ?><br />
        <?php echo $form['ordicity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordipostcode']->getLabel() ?><br />
        <?php echo $form['ordipostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordicouid']->getLabel() ?><br />
        {input ordicouid 'cols'=>30, 'class'=>'submitForm'}</td>
      </tr>
      </tbody>
  </table>
  </div>
  </fieldset>
  <table>
    <tbody>
      {if !$identity->isLoggedIn()} 
      <tr>
        <td><?php echo $form['antispam']->getLabel() ?><br />
        <?php echo $form['antispam']->control->cols(15) ?><small>{_'test proti robotum'}</small></td>
      </tr>
      {/if}
    </tbody>
  </table>
  <table>
      <tbody>
      <tr>
        <td><?php echo $form['ordnote']->getLabel() ?><br />
        <?php echo $form['ordnote']->control->cols(60)->rows(3) ?></td>
      </tr>
      </tbody>
  </table>
  <div class="submit">  <?php echo $form['makeorder']->getControl() ?>  </div>
  {/form}
 </div>
  {if !$identity->isLoggedIn()} 
  <script>
  $('#antispam').val('{!$presenter->config["ANTISPAM_NO"]}').closest('tr').hide();
  

  
  </script>
  {/if}
{/block}