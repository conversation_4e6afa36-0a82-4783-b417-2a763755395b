{default pageTitle       => $template->translate('Registrace nového zákazníka')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
  <h3>{_'Registrace nového zákazníka'}</h3>
  <div class="userform">
   {if $blockPromoRegistrace}{!$blockPromoRegistrace->pagbody}{/if}
  {form userAddForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$template->translate($error)}</li>
  </ul>
  
  <table>
    <tbody>
      <tr>
        <th><?php echo $form['usrmail']->getLabel()->class('required') ?></th>
        <td><?php echo $form['usrmail']->control->cols(15) ?><br /><small> {_'slouž<PERSON> zároveň jako <PERSON> j<PERSON>'}</small></td>
      </tr>
      <tr>
        <th><?php echo $form['usrpassw']->getLabel()->class('required') ?></th>
        <td><?php echo $form['usrpassw']->control->cols(15) ?><br /><small> {_'minimálně 6 znaků'}</small></td>
      </tr>
      <tr>
        <th><?php echo $form['usrpassw2']->getLabel()->class('required') ?></th>
        <td><?php echo $form['usrpassw2']->control->cols(15) ?><br /><small>{_'minimálně 6 znaků, podruhé pro kontrolu'}</small></td>
      </tr>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['usrmaillist']->control ?><?php echo $form['usrmaillist']->getLabel() ?></td>
      </tr>
      <tr>
        <th><?php echo $form['antispam']->getLabel() ?></th>
        <td><?php echo $form['antispam']->control->cols(15) ?><br /><small>  {_'test proti robotum'}</small></td>
      </tr>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['save']->getControl() ?></td>
      </tr>
    </tbody>
  </table>
  {/form}
  </div>
  <script>
  $('#antispam').val('{!$presenter->config["ANTISPAM_NO"]}').closest('tr').hide();
  </script>
{/block}