{* Catalog.detail.phtml *}

{* nastaveni promennych *}
{default pageTitle       => 'Rozdělení CD podle využití'}
{default pageKeywords    => 'Rozdělení CD podle využití'}
{default pageDescription => 'Rozdělení CD podle využití'}
{default catid => 0}

{block #content}

  <h3> {$pageTitle} </h3>
  <p>{_'Níže je uvedený úplný seznam různých možných způsobů využití pro hudební CD nahrávky s technologií Hemi-Sync®. Jednotlivé CD tituly jsou zařazeny podle účelu ke kterému byly vyvinuty. Některé tituly jsou zařazeny do více kategorií, j<PERSON><PERSON><PERSON> mimořádná mnohostrannost Hemi-Sync® umožňuje, aby byly použity k více ú<PERSON>elů<PERSON>.'}</p>
  <p>{_'Do<PERSON><PERSON><PERSON>u<PERSON> Vám, abyste byli ve Vašem výběru kreativní. I když mnoho produktů Hemi-Sync® je určeno k tomu, aby přispěly ke zlepšení zdraví, nemohou zastupovat lékařskou diagnózu či léčbu.'}</p>
    
  <ul>
  {foreach $menuCatalog2 as $catalog}
    {if  $catid != $catalog->catid}
    <li><a href="{plink Catalog:detail, $catalog->catid, $template->getURLKey($catalog->catname, Null)}">{$catalog->catname}</a></li>
    {else}
    <li><strong>{$catalog->catname}</strong></li>
    {/if}
  {/foreach}
  </ul>
  {if isset($productsData)}  
  {* vypis polozek zbozi *}
  <table id="productlist">
  {foreach $productsData as $row}
  <tr {if $iterator->isEven()} class="even" {/if}>
  <td><a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{$row->proname}</a></td>
  <td>{$row->catname} - {$row->procode}</td>  
  </tr>
  {/foreach}
  </table>
  {/if}
{/block}