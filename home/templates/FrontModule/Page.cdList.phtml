{* nastaveni promennych *}
{default pageTitle       => $page->pagtitle}
{default pageKeywords    => $page->pagkeywords}
{default pageDescription => $page->pagdescription}

{block #content}

  {!$page->pagbody}
    
  {* vypis hlavnich kategorii katalogu bez zarazeni dle pouziti *}
  <ul>
  {foreach $menuCatalog as $catalog}
    {if  $catid != $catalog->catid}
    <li><a href="{plink Page:detail 'id'=>$page->pagurlkey ,'catid'=>$catalog->catid}">{_'Vyber všechny CD'} {$catalog->catname}</a></li>
    {else}
    <li><strong>{$catalog->catname}</strong></li>
    {/if}
  {/foreach}
  {if  $catid > 0}
    <li><a href="{plink Page:detail $page->pagurlkey}">{_'Vyber všechny CD'}</a></li>
    {else}
    <li><strong>{_'Vyber všechny CD'}</strong></li>
    {/if}
  </ul>
    
  {* vypis polozek zbozi *}
  <table id="productlist">
  {foreach $productsData as $row}
  <tr {if $iterator->isEven()} class="even" {/if}>
  <td><a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{$row->proname}</a></td>
  <td>{$row->catname} - {$row->procode}</td>  
  </tr>
  {/foreach}
  </table>
{/block}