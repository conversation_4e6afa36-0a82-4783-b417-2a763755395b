<?php $template->register<PERSON><PERSON>per<PERSON>oader('Helpers::loader'); ?>
{contentType application/xml; charset=utf-8}
<?xml version="1.0"?>
<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">
<channel>
<title>{$presenter->config["SERVER_NAME"]}</title>
<link>{$baseUri}</link>
<description>{$presenter->config["HEADER_H2"]}</description>
{foreach $rows as $row}
<item>
<title>{if !empty($row->pronames)}{$row->pronames|truncate:70:''}{else}{$row->proname|truncate:70:''}{/if}</title>
<link>{plink //:Front:Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}</link>
<description>{$row->prodescs}</description>
<g:image_link>{$baseUri}/{!$template->getPicName($row, 'product/detail')}</g:image_link>
<?php 
$catPathIds = trim($row->catpathids, '|');
$pats = explode('|', $catPathIds);
$catPath = (string)$rootCats[$pats[0]];
$proType = str_replace('|', ' > ', $row->catpath); 
$priceVat = round((1+((int)$row->provat/100)) * $row->propricea, 0);
?>
<g:price>{$priceVat}</g:price>
<g:brand>{$row->manname}</g:brand>
{if !empty($row->procode2)}<g:mpn>{$row->procode2}</g:mpn>{/if}
<g:condition>new</g:condition>
<g:id>{$row->proid}</g:id>
<g:availability>{if $row->proaccess == 0}in stock{else}out of stock{/if}</g:availability>
{if !empty($catPath)}<g:google_product_category>{$catPath}</g:google_product_category>{/if}
{if !empty($proType)}<g:product_type>{$proType}</g:product_type>{/if}
</item>
{/foreach}
</channel>
</rss>