{default pageTitle       => (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{default pageKeywords    => $page->pagkeywords}
{default pageDescription => $page->pagdescription}

{block #content}
{if $menuIndex}
{if !empty($menuIndex->meicatid) || !empty($menuIndex->meiprocode)}
<a id="gotoeshop" href="
{if $menuIndex->meicatid > 0}
{plink Catalog:detail $menuIndex->catid, $template->getURLKey($menuIndex->catname, $menuIndex->catkey)}
{elseif !empty($menuIndex->meiprocode)}
{plink Product:detail $menuIndex->proid, $menuIndex->prokeymaster, $template->getProKey($menuIndex)}
{/if}
">Vstoupit do eshopu</a>
{/if}
{/if}
  {!$page->pagbody}
  {*
  {foreach $images as $row}
    {if $iterator->isFirst()}
  <div class="images">
    {/if}
    <img src="{$baseUri}/files/{$row->atafilename}" height="180" alt="{$row->ataname}" />
    {if $iterator->isLast()}
  </div>
    {/if}
  {/foreach}
  *}
  {foreach $attachments as $row}
    {if $iterator->isFirst()}
  <div class="attach">
    <h3>Přílohy</h3>
    <ul>
    {/if}
    <li><img src="{$baseUri}/ico/{$row->atatype}.png" width="16" height="16" alt="{$row->atatype}" /> <a href="{$baseUri}/files/{$row->atafilename}" target="_blank">{$row->ataname} ({$row->atasize|bytes})</a> </li>
    {if $iterator->isLast()}
    </ul>
    </div>
    {/if}
  {/foreach}
{/block}
