{* nastaveni promennych *}
{default pageTitle       => $page->pagtitle}
{default pageKeywords    => $page->pagkeywords}
{default pageDescription => $page->pagdescription}

{default catid => 0}

{block #content}

{!$page->pagbody}
  {block #cataloglist}
      {foreach $menuCatalog2 as $item}
        {if $iterator->isFirst()}  
    <ul>
        {/if}
      <li>{if  $catid != $item['data']->catid}<a href="{plink Catalog:detail, $item['data']->catid, $template->getURLKey($item['data']->catname, $item['data']->catkey)}">{$item['data']->catname}</a>{else}<strong>{$item['data']->catname}</strong>{/if}
        {if is_array($item['subitems'])} {include #this, menuCatalog2 => $item['subitems']}{/if}
      </li>
      {if $iterator->isLast()}
    </ul>
      {/if}
      {/foreach}
  {/block}
  {if isset($productsData)}  
  {* vypis polozek zbozi *}
  <table id="productlist">
  {foreach $productsData as $row}
  <tr {if $iterator->isEven()} class="even" {/if}>
  <td><a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{$row->proname}</a></td>
  <td>{$row->catname} - {$row->procode}</td>  
  </tr>
  {/foreach}
  </table>
  {/if}
{/block}