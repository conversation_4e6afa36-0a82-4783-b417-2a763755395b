<!DOCTYPE html><head>
<script id="usercentrics-cmp" src=https://web.cmp.usercentrics.eu/ui/loader.js data-settings-id="6pan3eq2NRib29" async></script>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<?php $template->registerHelperLoader('Helpers::loader'); ?>
{default $urlkey=>''}
{default $lng=>$lang}

<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title>{$pageTitle} - {$presenter->config["SERVER_NAME"]}</title>
{if isset($pageKeywords)}
<meta name="keywords" content="{$pageKeywords}" />
{/if}
{if isset($pageDescription)}
<meta name="description" content="{$pageDescription|strip|truncate:255, ''}">
{/if}
{if isset($pageRobots)}
<meta name="robots" content="{$pageRobots}" />
{/if}
<link rel="stylesheet" type="text/css" media="screen" href="{$baseUri}/css/default.css?v2">
<!--[if lte IE 6]>
<link rel="stylesheet" href="{$baseUri}/css/default_ie.css" type="text/css"  media="screen, projection">
<script src="{$baseUri}/js/DD_belatedPNG.js"></script>
<script>DD_belatedPNG.fix('.png_bg');</script>
<![endif]-->
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':
new Date().getTime(),event:'gtm.js' });var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
 } )(window,document,'script','dataLayer','GTM-KXMZ5PN5');</script>
<!-- End Google Tag Manager -->
<link rel="stylesheet" type="text/css" href="{$baseUri}/css/print.css" media="print">
<script type="text/javascript" src="{$baseUri}/js/jquery.js"></script>
<script type="text/javascript" src="{$baseUri}/js/netteForms.js"></script>
<script type="text/javascript" src="{$baseUri}/highslide/highslide-with-gallery.js"></script>
<script type="text/javascript" src="{$baseUri}/highslide/custom.js"></script>
<script type="text/javascript" src="{$baseUri}/js/functions.js?v2"></script>

<script type=text/javascript src="{$baseUri}/js/supersized.3.1.3.min.js"></script>
<script type=text/javascript>
			// Define slideshow_interval globally to prevent the error
      var slideshow_interval = 3000;
			
			jQuery(function($){
				$.supersized({
					autoplay: 0,  // Disable autoplay since there's only one image
					slide_interval: 3000,
					slides : [{
						image : '/img/motiv.jpg', title : '', url : ''
					}]
				});
		    });
		</script>

<link rel="shortcut icon" href="{$baseUri}/favicon.ico" type="image/x-icon">
<meta name="google-site-verification" content="qKnfiCCzuAU8bIUduU2ALcPlMbEl2_ZU6rTSEOxxj2s" />
	<!-- Facebook Pixel Code -->
	<script>
		!function(f,b,e,v,n,t,s){
			if(f.fbq)return;n=f.fbq=function()
			{
				n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)
			}
			;if(!f._fbq)f._fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)
		}
		(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');
		fbq('init', '1852427601658613');
		fbq('track', 'PageView');
	</script>
	<noscript><img height="1" width="1" style="display:none"
								 src="https://www.facebook.com/tr?id=1852427601658613&ev=PageView&noscript=1"
		/></noscript>
	<!-- DO NOT MODIFY -->
	<!-- End Facebook Pixel Code -->
	<!-- Smartsupp Live Chat script -->
	<script type="text/javascript">
		var _smartsupp = _smartsupp ||
						{
						};
		_smartsupp.key = '45b755ce4c99e481c14842eacf4f6b66a56ea45c';
		window.smartsupp||(function(d)
			{
			var s,c,o=smartsupp=function()
			{
				o._.push(arguments)
			}
			;o._=[];
			s=d.getElementsByTagName('script')[0];c=d.createElement('script');
			c.type='text/javascript';c.charset='utf-8';c.async=true;
			c.src='//www.smartsuppchat.com/loader.js?';s.parentNode.insertBefore(c,s);
		}
		)(document);
	</script>
</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KXMZ5PN5" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<!-- background -->
<div id="back" class="png_bg">
	<!-- main block -->
	<div id="main">
		<!-- header -->
		<div id="head" class="png_bg">
			<h1><a href="/">Prodej a servis manipulační techniky<span></span></a></h1>
			<h2>Čížek a Ptašek<br>
				servis vysokozdvižných vozíků<span></span></h2>
			<!-- search box -->
			<div id="search"> {form searchForm}
				<ul class="errors" n:if="$form->hasErrors()">
					<li n:foreach="$form->errors as $error">{$error}</li>
				</ul>
				{input name}
				{input quickSearch 'class'=>'button'}
				{/form searchForm} </div>
			<!-- search box -->

			<div class="nav__button"><a href="#nav">Katalog produktů</a></div>

			<div class="box basket">
				<h3>{_'Košík'}</h3>
				<p> {if $basketItemsCnt > 0}
					{_'Položek v košíku'}: <strong>{$basketItemsCnt}&nbsp;{_'ks'}</strong><br />
					{_'Cena bez DPH'}: <strong>{$basketPriceSum|formatPrice}</strong><br />
					<a href="{plink Basket:default}">{_'Košík'}</a> {else}
					{_'Košík je prázdný'}
					{/if} </p>
			</div>

			<!-- main menu -->
			<div id="menu"> {foreach $menuTop as $row}
				{if $iterator->isFirst()}
				<ul>
					<li>{ifCurrent Homepage:default}<strong>{_'Úvodní stránka'}</strong>{else}<a href="{plink Homepage:default}">{_'Úvodní strana'}</a>{/ifCurrent}</li>
					{/if}
					<li>{ifCurrent Page:detail, $row->pagurlkey}<strong>{$row->menname}</strong>{else}<a href="{plink Page:detail, $row->pagurlkey}">{$row->menname}</a>{/ifCurrent}</li>
					{if $iterator->isLast()}
				</ul>
				{/if}
				{/foreach} </div>
			<!-- main menu -->
		</div>
		<!-- header -->

		<!-- in block -->
		<div id="in">

			<!-- content -->
			<div id="content">
				<div class="facebook"><a href="https://www.facebook.com/dvaptaci/" target="_blank"><img src="{$baseUri}/img/facebook.jpg" width="192" height="40" alt="Facebook"></a></div>
				<div class="in"> {foreach $flashes as $flash}
					<div class="flash {$flash->type}">{$flash->message}</div>
					{/foreach}
					{include #content} </div>
			</div>
			<!-- content -->
			<!-- catalog -->
			<div id="nav">
				<div class="in">
					<h3>{_'Katalog'}</h3>
          {default $thisCatId=0}
          {define #menuCatalog}
          {foreach $items as $item}

          {if $iterator->isFirst()}
          <ul>
            {/if}
            <li>
            {if $thisCatId == $item->catid}
            <a class="active" href="{plink Catalog:detail, $item->catid, $item->catkeymaster, $template->getURLKey($item->catname, $item->catkey)}"><strong>{$item->catname}</strong></a>
            {else}
            <a class="png_bg" href="{plink Catalog:detail, $item->catid, $item->catkeymaster, $template->getURLKey($item->catname, $item->catkey)}">{$item->catname}</a>
            {/if}
            {ifset $menuCatalogL[$item->catid]}
            {include #menuCatalog 'items'=>$menuCatalogL[$item->catid]}{/ifset}
            </li>
            {if $iterator->isLast()}
          </ul>
            {/if}
          {/foreach}
          {/define}
          {include #menuCatalog 'items'=>$menuCatalog}

					{* Informace o nákupu *}
					{foreach $menuInfo as $row}
					{if $iterator->isFirst()}
					<h3>{$menuInfoTitle->menname}</h3>
					<ul>
						{/if}
						<li><a href="{plink Page:detail, $row->pagurlkey}">{$row->menname}</a></li>
						{if $iterator->isLast()}
					</ul>
					{/if}
					{/foreach}
					{if $userRow->usrid > 0}
					<div class="userform">
						<p> <a href="{plink User:default}">{_'Váš účet'}</a> <br />
							<a href="{plink User:logout}">{_'Odhlásit se'}</a> </p>
					</div>
					{else}
					<h3 class="other">{_'Přihlášení'}</h3>
					<div class="userform"> {form userLoginForm}
						{input usrmail} <br>
						{input usrpassw}
						{input submit}
						{/form userLoginForm}
						<p><a href="{plink User:sendPassword}">{_'Zapomenuté heslo'}</a> <br />
							<a href="{plink User:add}">{_'Nová registrace'}</a></p>
					</div>
					{/if}
        </div>
			</div>
			<!-- catalog -->

		</div>
		<!-- in block -->
	</div>
	<!-- main block -->
	<!-- footer -->
	<div id="foot">
		<div class="in">
			<p>
				&copy; 2005 - <?php echo date('Y') ;?> <a href="{$baseUri}">{$presenter->config["SERVER_NAME"]}</a> {ifset $blockFooter}, {!$blockFooter->pagbody}{/ifset}<br />
        <a href="#" onClick="UC_UI.showSecondLayer();">Nastavení cookies</a>
			</p>
		</div>
	</div>
	<!-- footer -->
</div>
<!-- background -->

<!-- modal -->
<div class="modal" id="modal-alert">

  <div class="modal__body">

    <div class="modal__close modal-close" id="modal-close">&times;</div>

      <a href="https://www.dvaptaci.cz/zimnivybava/k1001" class="modal-close"><img src="/img/zimni_vybava.png" alt="zimní výbava"></a>

  </div>

</div>

<script src="{$baseUri}/js/cookies.js"></script>

<script type="text/javascript">
  var alerted = getCookie('alert_132122');
  if (alerted !== 'yes') {
    //$("#modal-alert").addClass('is-open');
  }

  $( ".modal-close" ).click(function() {
    setCookie('alert_132122','yes',1);
    $("#modal-alert").removeClass('is-open');
  });


</script>

<script type="text/javascript">
/* <![CDATA[ */
var seznam_retargeting_id = 29480;
/* ]]> */
</script>
<script type="text/javascript" src="//c.imedia.cz/js/retargeting.js"></script>
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', 'UA-183431-27']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();

</script>
<!-- Kód Google značky pro remarketing -->
{if !empty($GLOBALS["ecommProId"])}
<script type="text/javascript">
var google_tag_params = {
ecomm_prodid: {!$GLOBALS["ecommProId"]},
ecomm_pagetype: '{!$GLOBALS["ecommPageType"]}',
ecomm_totalvalue: '{!$GLOBALS["ecommTotalValue"]}',
};
</script>
{/if}
<script type="text/javascript">
/* <![CDATA[ */
var google_conversion_id = *********;
var google_custom_params = window.google_tag_params;
var google_remarketing_only = true;
/* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
<div style="display:inline;">
<img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/*********/?value=0&amp;guid=ON&amp;script=0"/>
</div>
</noscript>

<script type="text/javascript">
	window.smartlook||(function(d) {
		var o=smartlook=function(){
			o.api.push(arguments)
		},h=d.getElementsByTagName('head')[0];
		var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
		c.charset='utf-8';c.src='//rec.smartlook.com/recorder.js';h.appendChild(c);
	})(document);
	smartlook('init', '1542d82b8e5beeac4cbdd8a8345baa44d43dc6b3');
</script>
{if !empty($userRow->usrmail)}
<script>
	smartlook('tag', 'email', '{!$userRow->usrmail}');
</script>
{/if}

<!-- SEEKY-->
<script type="text/javascript">
    var _paq = _paq || [];
    _paq.push(['trackPageView']);
    _paq.push(['enableLinkTracking']);
    (function() {
        var u="//ifirmy.cz/pxstats/";
        _paq.push(['setTrackerUrl', u+'piwik.php']);
        _paq.push(['setSiteId', 5829]);
        var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
        g.type='text/javascript'; g.async=true; g.defer=true; g.src=u+'piwik.js'; s.parentNode.insertBefore(g,s);
    })();
</script>
<noscript><p><img src="//ifirmy.cz/pxstats/piwik.php?idsite=5829" style="border:0;" alt="" /></p></noscript>
<!-- End SEEKY -->

{*<!-- LEADY start -->*}
<script type="text/javascript">
    var leady_track_key="9uZ2t7OFCLZN4yvT";
    (function(){
        var l=document.createElement("script");l.type="text/javascript";l.async=true;
        l.src='https://ct.leady.com/'+leady_track_key+"/L.js";
        var s=document.getElementsByTagName("script")[0];s.parentNode.insertBefore(l,s);
    })();
</script>
{*<!-- LEADY end -->*}

</body>
</html>
