{* Product.detail.phtml *}

{* nastaveni promennych *}
{default pageTitle       => empty($product->protitle) ? $product->proname : $product->protitle}
{default pageKeywords    => $product->prokeywords}
{default pageDescription => empty($product->prodescription) ? $template->striptags($product->prodescs) : $product->prodescription}



{block #content}
  <?php
  $GLOBALS["ecommProId"] = $product->proid;
  $GLOBALS["ecommPageType"] = 'product';
  $GLOBALS["ecommTotalValue"] = $template->countPriceVat($product);
?>
  <div id="detail">
   {if isset($catalogData)}
   <?php
     $catPath = $catalogData->catpathids;
     $isSim = (substr($catPath, 0, 4) == '|45|');
   ?>
   {if $isSim}<a href="{plink Catalog:detail, $catalogData->catid}">&lt; Zpět</a>{/if}
   {/if} 
   <h3> {$product->proname} </h3>
      <p class="detail"><a href="{$baseUri}/{$product|getPicName:'product/big'}" title="{_'Detail výrobku - kliknutím zvětšíte'}" class="highslide" onclick="return hs.expand(this,{ slideshowGroup: 'gallery' })">
  <img {!$template->getProPicSrcSize($product, 'detail', $baseUri)} alt="{$product->proname}"  />
    </a></p>
	
 <div class="lists">
  <ul>
  <li><strong>{_'Výrobce'}:</strong> {$manufacturer->manname}</li>
  <li><strong>{_'Katalogové číslo'}:</strong> {$product->procode}</li>
  <li><strong>{_'Objednávací číslo'}:</strong> {$product->procode2}</li>
  {if !empty($product->prowarranty)}<li><strong>{_'Záruční doba'}:</strong> {$product->prowarranty}</li>{/if}
  {if !$isVzor}<li>
      {if $product->proaccess == 0}
        <strong>{_'Dostupnost'}:</strong> {_'Skladem'}
      {elseif $product->proaccess == 90}
        <strong>{_'Dostupnost'}:</strong> {_'Na dotaz'}
      {elseif $product->proaccess == 99}
      {else}
        <strong>{_'Dostupnost'}:</strong> {_'dostupné do'} {$product->proaccess} {_'dnů'}
      {/if}
    </li>{/if}
  {if $product->proweight>0}<li><strong>{_'Hmotnost'}:</strong> {$product->proweight} Kg</li>{/if}
  <li><strong>{_'Naše cena'}: {$product|formatPriceVat}</strong></li>
  <li class="price"><strong><span>{_'Naše cena bez DPH'}:</span> {$product->proprice|formatPrice}</strong></li>
  <li class="owner">{$template->translate($enum_protype[$product->protypid])}</li> </ul>
 {if $product->proprice > 0 && !$isVzor}
 <p class="addbasket"> <a href="{plink Basket:add, $product->proid}" title="koupit">{_'Přidat do košíku'}</a></p>
 {/if}
 {if $product->proprice == 0 || $product->proprice > 10000 || $isVzor}
 <p class="addbasket"> <a href="#toggle" id="toggle" onclick="$('#reqform').toggle();">Poptávka</a></p>
 {/if}
 </div>
 {if $product->proprice == 0 || $product->proprice > 10000 || $isVzor}
 <br />
<div id="userform">
{form requestForm 'id'=>'reqform'}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$template->translate($error)}</li>
  </ul>
  <table>
    <tbody>
      <tr>
        <th>{label reqfirname /}: </th>
        <td>{input reqfirname}</td>
      </tr>
      <tr>
        <th>{label reqname 'class'=>"required" /}: </th>
        <td>{input reqname}</td>
      </tr>
      <tr>
        <th>{label reqmail 'class'=>"required" /}:</th>
        <td>{input reqmail}</td>
      </tr>
      <tr>  
        <th>{label reqphone 'class'=>"required"  /}:</th>
        <td>{input reqphone}</td>
      </tr>
      <tr>  
        <th>{label reqnote /}:</th>
        <td>{input reqnote 'rows'=>2, 'cols'=>60}</td>
      </tr>
      <tr>  
        <th>{label antispam 'class'=>"required" /}</th>
        <td>{input antispam} <small>test proti robotům</small></td>
      </tr>  
      <tr>
        <th>&nbsp;</th>
        <td>{input submit}</td>
      </tr>
    </tbody>
  </table>     
{/form}
</div>
<script>
$('#reqform').toggle();
$('#antispam').val('{!$presenter->config["ANTISPAM_NO"]}').closest('tr').hide();
</script>
 {/if}
 <h3 class="desc">{_'Popis produktu'}:</h3>
  {*<p>{!$product->prodescs}</p>*}
  {!$product->prodesc}
  {foreach $proParams as $row}
    {if $iterator->isFirst()}
 <h3>{_'Parametry zboží'}</h3>   
 <div class="proparams">
   <table> 
    {/if}
    <tr {if $iterator->isEven()} class="even"{/if}><th>{$row->prpname}:</th><td>{$row->prpvalue}</td></tr>
    {if $iterator->isLast()}
   </table>
  </div> 
    {/if}
  {/foreach}
  {* dalsi obrazky *}
  {foreach $images as $row}
    {if $iterator->isFirst()}
  <div id="nextimg">
    <h3>Další obrázky</h3>
    {/if}
   <p> <a href="{$baseUri.'/pic/product/big/'.$row["name"]}" title="{_'Detail výrobku - kliknutím zvětšíte'}" class="highslide" onclick="return hs.expand(this,{ slideshowGroup: 'gallery' })">
  <img src="{$baseUri.'/pic/product/list/'.$row["name"]}" width="{$row["w"]}"  height="{$row["h"]}" alt="{$product->proname}"  />
    </a> </p>
	  {if $iterator->isLast()}
  </div>  
    {/if}
  {/foreach}
  {* prilohy *}
  {foreach $files as $row}
    {if $iterator->isFirst()}
  <div class="attach">
    <h3>Přílohy</h3>
    <ul>
    {/if}
    <li><img src="{$baseUri}/ico/{$row->atatype}.png" width="16" height="16" alt="{$row->atatype}" /> <a href="{$baseUri}/files/{$row->atafilename}" target="_blank">{$row->ataname} ({$row->atasize|bytes})</a> </li>
    {if $iterator->isLast()}
    </ul>
  </div>  
    {/if}
  {/foreach}

 {* drobecky katalogu 
 <div class="crumb">
  <h4>
  {foreach $catalogs as $catalog}
  <?php 
  $arrid = explode("|", trim($catalog->catpathids, '|'));
  $arrname = explode("|", trim($catalog->catpath, '|'));
  $arrCatPath = array_combine($arrid, $arrname); 
  ?>
    {foreach $arrCatPath as $id => $name}
      <a href="{plink Catalog:detail, $id}">{$name}</a>{if !$iterator->isLast()}&nbsp;&gt;{/if}
    {/foreach}
    {if !$iterator->isLast()}<br />{/if}
  {/foreach}
  </h4>
</div>
*}
</div>
  {ifset $_SERVER["HTTP_REFERER"]}
  <a href="{$_SERVER["HTTP_REFERER"]}" id="gotoback">Zpět</a>
  {/ifset}
{/block}