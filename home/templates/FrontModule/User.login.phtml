{default pageTitle       => $template->translate('<PERSON><PERSON><PERSON><PERSON><PERSON>š<PERSON>í z<PERSON>azník<PERSON>')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
<h3>{_'P<PERSON><PERSON>lášení zákazníka'}</h3>
<div class="userform">
  {form userLoginForm} 
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$template->translate($error)}</li>
  </ul>
  
  <table>
    <tbody>
      <tr>
        <th><?php echo $form['usrmail']->getLabel()->class('required') ?></th>
        <td><?php echo $form['usrmail']->control->cols(15) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrpassw']->getLabel()->class('required') ?></th>
        <td><?php echo $form['usrpassw']->control->cols(15) ?></td>
      </tr>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['submit']->getControl() ?></td>
      </tr>
    </tbody>
  </table>
  {/form}
</div> 
{/block}