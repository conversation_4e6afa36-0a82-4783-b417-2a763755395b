{default pageTitle       => $presenter->config["INDEX_TITLE"]}
{default pageDescription => $presenter->config["INDEX_DESC"]}
{default pageKeywords => $presenter->config["INDEX_KEYWORDS"]}

  {block #content}

          {foreach $menuIndexs as $row}
            {if $iterator->isFirst()}
          <div id="next">
            {*
            <div class="next"><p>{ifset $menuIndexsBig[0]}{include #menuIndexLink, 'iRow'=>$menuIndexsBig[0]}{/ifset}</p></div>
            <div class="next"><p>{ifset $menuIndexsBig[1]}{include #menuIndexLink, 'iRow'=>$menuIndexsBig[1]}{/ifset}</p></div>
            *}
            {/if}
            <div class="next"><p>{include #menuIndexLink, 'iRow'=>$row}</p></div>
            {if $iterator->isLast()}
          </div>
            {/if}
          {/foreach}

         <!--catalog - new -->
         <form action="#">
      <div id="kat">
            <div id="katin">
              {foreach $productsData as $row}
              <div class="kat">
                <div class="in">
                  <h4 class="none"><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}">{$row->proname|truncate:70}</a></h4>
                  <span><a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}"><img {!$template->getProPicSrcSize($row, 'list', $baseUri)} alt="{$row->proname} - {_'zobrazit detail zboží'}" /></a></span>
                  <p>{$row->prodescs|truncate:60}</p>
                  <p class="price"> <strong>{$row->proprice|formatPrice}</strong> {if $row->proprice > 0}<a class="adbasket" href="{plink Basket:add, $row->proid}" title="{_'Přidat do košíku'}">{_'Do košíku'}</a> {*<input type="text" size="10" value="1">*}{/if} <a href="{plink Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}" class="adetail">{_'Detail'}</a></p>
                  <hr />
                </div>
              </div>
              {if $iterator->counter % 4 == 0}
              <div class="end"><hr></div>
              {/if}


              {/foreach}
            </div>
            <div class="end">
              <hr>
            </div>
          </div>
      </form>
          <!--catalog- new  -->

{/block}

{block #menuIndexLink}
<a href="
{if $iRow->meipagid > 0}
{plink Page:detail $iRow->pagurlkey}
{elseif $iRow->meicatid > 0}
{plink Catalog:detail $iRow->catid, $template->getURLKey($iRow->catname, $iRow->catkey)}
{elseif !empty($iRow->meiprocode)}
{plink Product:detail $iRow->proid, $iRow->prokeymaster,  $template->getProKey($iRow)}
{/if}
">{if $iRow->meibig==1}<img src="{$baseUri}/pic/menuindex/{$iRow->meiid}.jpg" width="282" height="125" alt="{$iRow->meiname}"><br><span>{$iRow->meiname}</span>{else}<img src="{$baseUri}/pic/menuindex/{$iRow->meiid}.jpg" width="140" height="110" alt="{$iRow->meiname}"><br><span>{$iRow->meiname}</span>{/if}</a>
{/block}
