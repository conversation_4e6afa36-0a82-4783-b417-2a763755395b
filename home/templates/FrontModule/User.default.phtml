{default pageTitle       => $template->translate('<PERSON><PERSON><PERSON> úč<PERSON>')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
  <h3> {_'<PERSON><PERSON><PERSON> úč<PERSON>'} </h3>
  
  <p>[ <a href="{plink User:edit}">{_'Upravit údaje'} / {_'změnit heslo'}</a> ]</p>
  <h3>{_'Otevřené objednávky'}</h3>
  {if count($openedOrders) > 0}
  {foreach $openedOrders as $row}
    {if $iterator->isFirst()} 
    <table>
    {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>   
      <td>{$row->ordprice|formatPrice}</td>
      <td>{$template->translate($enum_ordstatus[$row->ordstatus])}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {else}
    <p>{_'Žádné otevřené objednávky'}</p>
  {/if}
  
  <h3>{_'Uzavřené objednávky'}</h3>
  {if count($closedOrders) > 0}
  {foreach $closedOrders as $row}
    {if $iterator->isFirst()} 
    <table class="grid">
     
    {/if}
    <tr>
      <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
      <td>{$row->orddatec|date:'%d.%m.%Y'}</td>   
      <td>{$row->ordprice|formatPrice}</td>
      <td>{$template->translate($enum_ordstatus[$row->ordstatus])}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {else}
    <p>{_'Žádné uzavřené objednávky'}</p>
  {/if}
{/block}