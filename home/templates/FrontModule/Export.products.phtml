<?php $template->register<PERSON><PERSON><PERSON><PERSON>oader('Helpers::loader'); ?>
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf8"?>
<SHOP>
{foreach $rows as $row}
<SHOPITEM>
<PRODUCT>{if !empty($row->pronames)}{$row->pronames}{else}{$row->proname}{/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->prokeymaster, $row->proid, $template->getProKey($row)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>1</DELIVERY_DATE>
<IMGURL>{$baseUri}/{!$template->getPicName($row, 'detail')}</IMGURL>
<PRICE_VAT>{$row->proprice}</PRICE_VAT>
</SHOPITEM>
{/foreach}
</SHOP>