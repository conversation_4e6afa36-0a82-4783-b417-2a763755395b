{default pageTitle       => $template->translate('Upravit osobní údaje')}
{default pageRobots      => "nofollow,noindex"}

{block #content}
  <h3>{_'Upravit osobní údaje'}</h3>
  <div class="userform">
  
  {form userEditForm}
  
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$template->translate($error)}</li>
  </ul>
  
  <table>
    <tbody>
      <tr>
        <th><?php echo $form['usrmail']->getLabel() ?></th>
        <td><?php echo $form['usrmail']->control->cols(15) ?> <small>{_'slouží z<PERSON>roveň jako přihlašovací jméno'}</small></td>
      </tr>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['usrmaillist']->control ?><?php echo $form['usrmaillist']->getLabel() ?></td>
      </tr>
    </tbody>
  </table>
  <fieldset>
    <legend>{_'Fakturační a současně doručovací adresa'}</legend>
    <table>
      <tbody>
      <tr>
        <th><?php echo $form['usriname']->getLabel() ?></th>
        <td><?php echo $form['usriname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrilname']->getLabel() ?></th>
        <td><?php echo $form['usrilname']->control->cols(30) ?></td>
      </tr>
      {if $lang=='cs'}
      <tr>
        <th><?php echo $form['usrifirname']->getLabel() ?></th>
        <td><?php echo $form['usrifirname']->control->cols(30) ?></td>
      </tr>
      {/if}
      <tr>
        <th><?php echo $form['usristreet']->getLabel() ?></th>
        <td><?php echo $form['usristreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <th><?php echo $form['usristreetno']->getLabel() ?></th>
        <td><?php echo $form['usristreetno']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <th><?php echo $form['usricity']->getLabel() ?></th>
        <td><?php echo $form['usricity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usripostcode']->getLabel() ?></th>
        <td><?php echo $form['usripostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th>{label usricouid}{_'Země'}{/label}</th>
        <td><?php echo $form['usricouid']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrtel']->getLabel() ?></th>
        <td><?php echo $form['usrtel']->control->cols(30) ?></td>
      </tr>
    </tbody>
  </table>
  {if $lang=='cs'}  
  <table id="icDic">  
    <tbody>  
      <tr>
        <th><?php echo $form['usric']->getLabel() ?></th>
        <td><?php echo $form['usric']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrdic']->getLabel() ?></th>
        <td><?php echo $form['usrdic']->control->cols(30) ?></td>
      </tr>
      </tbody>
  </table>
  {/if}
  </fieldset>
  <fieldset>
    <legend>{_'Adresa dodání'}</legend>
    <table>
      <tbody>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['stadr']->control ?><?php echo $form['stadr']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>  
    <div id="sendBox">
    <table>
      <tbody>
      <tr>
        <th><?php echo $form['usrstname']->getLabel() ?></th>
        <td><?php echo $form['usrstname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrstlname']->getLabel() ?></th>
        <td><?php echo $form['usrstlname']->control->cols(30) ?></td>
      </tr>
      {if $lang=='cs'}
      <tr>
        <th><?php echo $form['usrstfirname']->getLabel() ?></th>
        <td><?php echo $form['usrstfirname']->control->cols(30) ?></td>
      </tr>
      {/if}
      <tr>
        <th><?php echo $form['usrststreet']->getLabel() ?></th>
        <td><?php echo $form['usrststreet']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <th><?php echo $form['usrststreetno']->getLabel() ?></th>
        <td><?php echo $form['usrststreetno']->control->cols(30) ?></td>
      </tr>  
      <tr>
        <th><?php echo $form['usrstcity']->getLabel() ?></th>
        <td><?php echo $form['usrstcity']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th><?php echo $form['usrstpostcode']->getLabel() ?></th>
        <td><?php echo $form['usrstpostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <th>{label usrstcouid}{_'Země'}{/label}</th>
        <td><?php echo $form['usrstcouid']->control->cols(30) ?></td>
      </tr>
      </tbody>
  </table>
  </div>
  </fieldset>
  <fieldset>
    <legend>{_'Změna hesla'}</legend>
    <table>
      <tbody>
      <tr>
        <th><?php echo $form['usrpassw_old']->getLabel() ?></th>
        <td><?php echo $form['usrpassw_old']->control->cols(15) ?> <small>{_'vyplňte jen pokud chcete heslo změnit'}</small></td>
      </tr>
      <tr>
        <th><?php echo $form['usrpassw']->getLabel() ?></th>
        <td><?php echo $form['usrpassw']->control->cols(15) ?> <small>{_'minimálně 6 znaků'}</small></td>
      </tr>
      <tr>
        <th><?php echo $form['usrpassw2']->getLabel() ?></th>
        <td><?php echo $form['usrpassw2']->control->cols(15) ?> <small>{_'minimálně 6 znaků, podruhé pro kontrolu'}</small></td>
      </tr>
      </tbody>
    </table>
  </fieldset>
  <table>
    <tbody>
      <tr>
        <th>&nbsp;</th>
        <td><?php echo $form['save']->getControl() ?></td>
      </tr>
    </tbody>
  </table>
  {/form}
  </div>
{/block}