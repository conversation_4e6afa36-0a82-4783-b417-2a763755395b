<?php $template->registerHelper<PERSON>oader('Helpers::loader'); ?>
{if $ordSpecDel}
<h3><PERSON><PERSON><PERSON> č. {$orderRow->ordcode} {_'vytvořený na'} {$presenter->config["SERVER_NAME"]} byl přijat ke zpracování</h3>
{else}
<h3>{_'Vaše objednávka č.'} {$orderRow->ordcode} {_'vytvořené na'} {$presenter->config["SERVER_NAME"]}</h3>
{/if}
<strong>Děkujeme, že využíváte našich služeb. </strong><br />
<br />
{if $ordSpecDel}
  <strong>Vaše objednávka byla změněna v poptávku. Náš obchodník Vás bude kontaktovat (upřesnění technických parametrů, mont<PERSON><PERSON>, doprava, ...)</strong><br /><br />
{/if}
<strong>{_'Polo<PERSON>ky objednávky'}:</strong><br />
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Objednací číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Cena bez DPH</strong></td>
</tr>
<?php
$sum = 0;
?>
{foreach $ordItemRows as $row}
<?php
  if ($row->oritypid == 0) $sum += ($row->oriqty*$row->oriprice);
?>
{if $ordSpecDel && $row->oritypid == 1}
{else}
<tr>
  <td>{$row->oriprocode}</td>
  <td>{$row->oriprocode2}</td>
  <td>
    {if $row->oriproid > 0}
    <a href="{plink //:Front:Product:detail, $row->oriproid, "", ""}">{$row->oriname}</a>
    {else}
    {$row->oriname}
    {/if}
  </td>
  <td>{$row->oriqty} {_'ks'}</td>
  {if $row->oritypid != 1}
  <td>{$row->oriprice|formatPrice}</td>
  {else}
  <td>{if $row->oriprice > 0}{$row->oriprice|formatPrice}{else}bude stanovena{/if}</td>
  {/if}
</tr>
{/if}
{/foreach}
</table>
<br />
<table>
<tr>
  <td><strong>{_'Cena za zboží'}: </strong></td>
  <td>{$sum|formatPrice}</td>
</tr>
<tr>
  <td><strong>{_'Celková cena bez DPH'}: </strong></td>
  <td>{$orderRow->ordprice|formatPrice}</td>
</tr>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$orderRow->ordpricevat|formatPrice}</td>
</tr>
</table>
<p>
<strong>{_'Hmotnost'}:</strong> {$orderRow->ordweight|number:2:',':' '}&nbsp;Kg<br />
<strong>{_'Zvolená doprava'}:</strong> {$delMode->delname}<br />
<strong>{_'Platba'}:</strong> {$payMode->delname}<br />
<strong>{_'Poznámka'}:</strong><br />
{$orderRow->ordnote|nl2br}<br />
<br />
<strong>{_'Fakturační a současně doručovací adresa'}:</strong><br />
{_'Jméno'}: {$orderRow->ordiname}<br />   
{_'Přijmení'}: {$orderRow->ordilname}<br />   
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br />{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br /> 
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br />      
{_'Město, obec'}: {$orderRow->ordicity}<br />      
{_'PSČ'}: {$orderRow->ordipostcode}<br /> 
{_'Telefon'}: {$orderRow->ordtel}<br />      
{_'Email'}: {$orderRow->ordmail}<br />      
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br />
{if !empty($orderRow->ordstname)}
<br />   
<strong>Dodací adresa:</strong><br />
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br />   
Firma: {$orderRow->ordstfirname}<br />
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br />      
Město, obec: {$orderRow->ordstcity}<br />      
PSČ: {$orderRow->ordstpostcode} {$enum_Countries[$orderRow->ordstcouid]}<br />
{/if}
</p>
{include 'mailFooterOrder.phtml'}