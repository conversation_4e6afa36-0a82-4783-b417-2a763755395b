<?php $template->register<PERSON><PERSON><PERSON><PERSON>oader('Helpers::loader'); ?>
<PERSON><PERSON><PERSON><PERSON><PERSON> zákazníku,<br />
{$orderRow->ordiname} {$orderRow->ordilname}, {$orderRow->ordistreet} {$orderRow->ordistreetno}, {$orderRow->ordicity}, {$orderRow->ordipostcode}, {$enum_Countries[$orderRow->ordicouid]}<br />
Dne {$ordDateSend|date:'d.m.Y'} jsme odesílali na Vaši adresu zásilku z internetového obchodu {$presenter->config["SERVER_NAME"]}<br />
<strong>{_'Polo<PERSON><PERSON> objednávky'}:</strong>
<table>
<?php
$sum = 0;
?>
{foreach $ordItemRows as $row}
<?php
  if ($row->oritypid == 0) $sum += ($row->oriqty*$row->oriprice);
?>
<tr>
<td>{$row->oriname}</td>
<td>{$row->oriqty} {_'ks'}</td>
<td>{$row->oriprice|formatPrice}</td>
</tr>
{/foreach}
<tr>
<td><strong>{_'Cena za zboží'}: </strong></td>
<td></td>
<td>{$sum|formatPrice}</td>
</tr>
<tr>
<td><strong>{_'Celková cena'}: </strong></td>
<td></td>
<td>{$orderRow->ordprice|formatPrice}</td>
</tr>
</table>
Pracovník pošty Vás pravděpodobně nezastihl doma a zásilku máte uloženou na poště.<br />
Tímto Vás žádáme abyste si zásilku co nejdříve vyzvedl-a. Již se blíží termín kdy pošta vrátí zásilku zpět odesílateli.<br />
{if !empty($orderRow->ordparcode)}Číslo zásilky: {$orderRow->ordparcode}.<br />{/if}
Pokud jste si již zásilku vyzvdl-a potom považujte tento email za bezpředmětný.<br />