{block #content}
  {default $dataRow = false}
  {var $tab = $presenter->getParam('tab')}   
  
  <h3>{block #title}{if $dataRow} {$dataRow->proname} {else} Nová položka {/if}{/block}</h3>
  {if $dataRow} <p><a href="{plink :Front:Product:detail, $dataRow->proid, $dataRow->prokeymaster, $template->getProKey($dataRow)}">Detail zboží ve veřej<PERSON></a></p>{/if}
  <script type="text/javascript">
  $(document).ready(function() {
    $(function() {
      $("#tabs").tabs();
      {if !empty($tab)}
      //var index = $('#tabs ul').index($('#{!$tab}'));
      //$('#tabs ul').tabs('select', index);
      $("#tabs").tabs("select","{!$tab}");
      {/if}
    });  
  });
  </script>
  
  <div id="tabs">
   <ul>
      <li><a href="#tabs_editmain"><PERSON><PERSON><PERSON><PERSON><PERSON> ú<PERSON></a></li>
      <li><a href="#tabs_editcatalog">Zařazení do katalogu</a></li>
      <li><a href="#tabs_editdesc">Dlouhý popis</a></li>
      <li><a href="#tabs_seo">SEO</a></li>
      <li><a href="#tabs_editrem">Rozšířené údaje</a></li>
      <li><a href="#tabs_pic">Obrázky</a></li>
      <li><a href="#tabs_attachment">Přílohy</a></li>
      <li><a href="#tabs_param">Parametry</a></li>
      <!--<li><a href="#tabs_stat">Staistika</a></li>-->
   </ul>
   {control productEditForm}
   <br>
   <a href="{plink detachImages $dataRow->proid}" class="default button">Odpárovat obrázky produktu</a>
</div>
<script type="text/javascript" src="{$baseUri}/js/textarea_maxlen.js"></script>    
{/block}