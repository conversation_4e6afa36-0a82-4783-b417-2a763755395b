<?php $template->register<PERSON><PERSON><PERSON><PERSON>oader('Helpers::loader'); ?>
<table class="grid">
  <tr>
    <th>Č. obj.</th>
    <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>
    <th>Zákazník</th>
    <th>Firma</th>
    <th>Datum</th>
    <th>Cena bez DPH</th>
    <th><PERSON><PERSON><PERSON>, platba</th>
    <th>Datum exp.</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th colspan="4"></th>
  </tr>
  <?php
    $colors[0] = "#FFFFFF"; //ceka na zpracovani
    $colors[1] = "#FFFFC0"; //Vyřizuje se
    $colors[2] = "#FFBE7D"; //Čeká na platbu
    $colors[3] = "#C0C0FF"; //Odeslána
    $colors[4] = "#C0FFC0"; //Uzavřená
    $colors[5] = "#FFC0C0"; //Stornovaná
    $colors[6] = "#800080"; //Zaplaceno
    $colors[7] = "#808080"; //cerna listina
    
    $sum = "";
  ?>
  {foreach $dataRows as $row}
  <?php
    $sum += $row->ordprice;
  ?>
  {var $style= (!$iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '')}
  <tr {$style}>
    <td>{$row->ordcode}</td>
    <td>{$row->admname}</td>
    <td>{$row->ordiname} {$row->ordilname}|{$row->ordstname} {$row->ordstlname}</td>
    <td>{$row->ordifirname}|{$row->ordstfirname}</td>
    <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
    <td style="text-align: right;">{$row->ordprice|formatPrice}</td>
    <td>{$row->delnamemas}, {$row->delname}</td>
    <td>{$row->datesend|date:'%d.%m.%Y'}</td>
    <td>{if !empty($row->ordnote)}<a href="#" onclick="return noteSH('note_{$row->ordid}');"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" /></a><div style="display:none" id="note_{$row->ordid}">{!$row->ordnote|nl2br}</div>{/if}</td>
    <td style="color: black; background-color: {!$colors[$row->ordstatus]};">{$enum_ordstatus[$row->ordstatus]}</td>
    <td>{if $row->oriprobigsize > 0}<img src="{$baseUri}/ico/bigsize.png" width="16" height="16"  title="obsahuje nadrozměrné zboží" /> {/if}{if $row->oriprooffer > 0} <img src="{$baseUri}/ico/prooffer.png" width="16" height="16" title="obsahuje nabídkové zboží" />{/if}</td>
    <td><a href="{plink Order:edit, $row->ordid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" /></a></td>
    <td>{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}"><img src="{$baseUri}/ico/user.png" width="16" height="16" /></a>{/if}</td>
    <td><a target="invoicePrint" href="{plink Order:printInvoice, $row->ordid}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" /></a> <a href="{plink Order:printInvoice, $row->ordid, 'D'}"><img src="{$baseUri}/ico/export.png" width="16" height="16" /></a></td>
  </tr>
  {/foreach}
  <tr>
    <td colspan="5" ><strong>Celkem:</strong>    </td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong>    </td>
    <td colspan="9" ></td>
  </tr>
  </table>