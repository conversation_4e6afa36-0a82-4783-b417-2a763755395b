{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('oriname', 'oriproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('oriname', 'oriprice');
          $("#"+newId).val(ui.item.price);  
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title}Objednávka č. {$dataRow->ordid} ({$dataRow->ordiname} {$dataRow->ordilname} | {$dataRow->ordstname} {$dataRow->ordstlname}) {/block}</h3>
  <table>
    <tr>
      <td>{control orderChangeStateForm}</td>
      <td style="border: 1px solid black">
        Jméno: příjmení <input type="text" value="{$dataRow->ordiname} {$dataRow->ordilname}"  onClick="this.select();">
        <br>
        <textarea cols="60" rows="5"  onClick="this.select();">{$dataRow->ordiname} {$dataRow->ordilname}
{$dataRow->ordistreet} {$dataRow->ordistreetno}
{$dataRow->ordipostcode} {$dataRow->ordicity}
Email: {$dataRow->ordmail}, tel: {$dataRow->ordtel}
IČ: {$dataRow->ordic}, DIČ: {$dataRow->orddic}</textarea>
      </td>
    </tr>
  </table>
  <h4>Položky objednávky</h4>
  {form ordItemsEditForm}
    <table class="grid">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>Objednávací č.</th>
        <th>název</th>
        <th>cena</th>
        <th>počet</th>
        <th></th>
      </tr>
      {foreach  $form['items']->getComponents() as $cont}
      {if $cont->name != 'delivery'}
      {var $oriid=$form['items'][$cont->name]['oriid']->value}
      <tr>
        <td><?php echo $form['items'][$cont->name]['oriproid']->control ?></td>
        <td>{$ordItems[$oriid]->oriprocode}</td>
        <td>{$ordItems[$oriid]->oriprocode2}</td>
        <td><?php echo $form['items'][$cont->name]['oriname']->control ?></td>
        <td><?php echo $form['items'][$cont->name]['oriprice']->control ?></td>
        <td><?php echo $form['items'][$cont->name]['oriqty']->control ?></td>
        <td><a href="{plink Order:deleteItem, $form['items'][$cont->name]['oriid']->value, (int)$presenter->getParam('id')}" onclick="return DeleteConfirm('položku objednávky {!$form['items'][$cont->name]['oriname']->value}');"> <img src="{$baseUri}/ico/delete.png" width="16" height="16" /> SMAZAT </a></td>
      </tr>
      {else}
      <tr>
        <td>poštovné</td>
        <td></td>
        <td></td>
        <td><?php echo $form['items'][$cont->name]['oriname']->control ?> </td>
        <td><?php echo $form['items'][$cont->name]['oriprice']->control ?></td>
        <td>1</td>
        <td></td>
      </tr>
      {/if}
      {/foreach}
      {if $ordItemDisc}
      <tr>
        <th colspan="7">Sleva</th>
      </tr>
      <tr>
        <td></td>
        <td></td>
        <td></td>
        <td>{$ordItemDisc->oriname}</td>
        <td>{$ordItemDisc->oriprice}</td>
        <td>1</td>
        <td></td>
      </tr>
      {/if}
      <tr>
        <th colspan="7">nová položka</th>
      </tr>
      <tr>
        <td><?php echo $form['newitem']['oriproid']->control ?></td>
        <td></td>
        <td></td>
        <td><?php echo $form['newitem']['oriname']->control ?> </td>
        <td><?php echo $form['newitem']['oriprice']->control ?></td>
        <td><?php echo $form['newitem']['oriqty']->control ?></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="7">{input saveitems}</td>
      </tr>
    </table>
  {/form}
  
  {control orderEditForm}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie objednávky</h3>
    <table>
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    </tr>
    {if $iterator->isLast()}
    </table>  
    {/if}
  {/foreach}
  {/block}
