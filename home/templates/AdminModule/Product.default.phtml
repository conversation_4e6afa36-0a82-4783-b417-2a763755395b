{block #content}
  <script type="text/javascript">
  var basePath = {$baseUri};
  </script>
  <script type="text/javascript" src="{$baseUri}/js/autocompleteCatIdProductDefault.js"></script>

  <h3>{block #title}Seznam zboží{/block}</h3>
  
  {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /}: {input code}
  {label code2 /}: {input code2}
  {label name /}: {input name}
  {label manid /}: {input manid}<br />

  {label catid /}: {input catid size=>3}
  <a href="#" onclick="return clearAc('search');"><img src="{$baseUri}/ico/delete.png" width="16" height="16" title="vymazat katalog"></a>
  {input catname size=>60, placeholder=> "Zadejte název kategorie ... "}
  {label orderby /}: {input orderby} {input orderbytype}
  {input search}
  {input clear}<br>
  {label catid2 /}: {input catid2}
  </fieldset>
  {/form}
  
 {control paginator}
{form listEditForm}  
 <table class="grid">
  <tr>
    <th>Katalogové č.</th>
    <th>Objednávací č.</th>
    <th>Název</th>
    <th>Výrobce</th>
    <th>Cena bez DPH</th>
    <th colspan="2">Dostupnost</th>
    <th>Prodáno</th>
    <th>Pořadí</th>
    <th>Status</th>
    <th colspan="4"></th>
  </tr>
  {foreach $dataRows as $row}
    {var $container = $form[$row->proid]}
    <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      <td>{$row->procode}</td>
      <td>{$row->procode2}</td>
      <td>{$row->proname}</td>
      <td>{$row->manname}</td>
      <td>{$row->propricea}</td>
      <td>{$row->proaccess}</td>
      <td><?php echo $container["proaccess"]->getControl()->addAttributes(array('size'=>1)) ?></td>
      <td>{$row->prscnt} ks</td>
      <td>{$row->proorder}</td>
      <td>{$enum_prostatus[$row->prostatus]}</td>
      <td>{if $row->probigsize > 0}<img src="{$baseUri}/ico/bigsize.png" width="16" height="16"  title="nadrozměrné zboží" /> {/if}{if $row->prooffer > 0} <img src="{$baseUri}/ico/prooffer.png" width="16" height="16" title="nabídkové zboží" />{/if}</td>
      <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, $row->prokeymaster, $template->getProKey($row)}"><img src="{$baseUri}/ico/front.png" width="16" height="16" alt="zobrazit zboží ve veřejné části" title="zobrazit zboží ve veřejné části" /></a></td>
      <td><a href="{plink Product:delete, $row->proid}" onclick="return DeleteConfirm('položku {!$row->proname}');"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /></a></td>
    </tr>
  {/foreach}
  </table>
  {input save}
  {/form listEditForm}
  {control paginator} 
{/block}
