{* 
C.default.phtml

Array $items['subitems'] - array urovni katalogu
Array $enum_catstatus    - ciselnik statusu urovne katalogu
*}

{block #content}

<script>
  function toggle(id) {
    $("." + id).toggle();
    return false;
  }
  function rollDownAll() {
    $(".subitems").show();
    return false;
  }
  function rollUpAll() {
    $(".subitems").hide();
    return false;
  }
</script>

<h3>{block #title}Katalog{/block}</h3>
  <p><a href="#" onclick="return rollDownAll();">rozbalit vše</a> | <a href="#" onclick="return rollUpAll();">sbalit vše</a></p>
  {block #cataloglist}
    {foreach $items as $item}
  <ul {if $item['data']->catlevel == 2} class="cat_{$item['data']->catmasid} subitems" style="display: none;"{/if}>
    <li>[{$item['data']->catorder}] {if $item['data']->catstatus==0}<strong>{$item['data']->catname}</strong>{else}<s>{$item['data']->catname}</s>{/if} ({$item['data']->catid})
      {if $item['data']->catmasid == 0}
      <a href="#">&nbsp;<span style="font-size: large;" onclick="return toggle('cat_' + {$item['data']->catid});">&darr;</span>&nbsp;</a>
     {/if}
    <a href="{plink Catalog:edit, $item['data']->catid}"><img src="{$baseUri}/ico/edit.png" width="16" height="16" /></a>
    <a href="{plink Catalog:edit, 0, catmasid=>$item['data']->catid}"><img src="{$baseUri}/ico/add.png" width="16" height="16" /></a>
    <a href="{plink Catalog:delete, $item['data']->catid}" onclick="return DeleteConfirm('katalog {!$item['data']->catname}');"><img src="{$baseUri}/ico/delete.png" width="16" height="16" /></a>
      {if is_array($item['subitems'])} {include #this, items => $item['subitems']}{/if}
    </li>
  </ul>
    {/foreach}
  {/block}

{/block}
