-- upravy menuindex
ALTER TABLE `menuindexs` DROP COLUMN `meisrctype`, DROP COLUMN `meiurlsys`,    ADD COLUMN `meicatid` INT DEFAULT '0' NOT NULL AFTER `meipagid`,     ADD COLUMN `meiprocode` VARCHAR(255) NULL AFTER `meicatid`,    CHANGE `meisrcid` `meipagid` MEDIUMINT(9) DEFAULT '0' NOT NULL COMMENT 'ID zaznamu';
ALTER TABLE `menuindexs` ADD COLUMN `meibig` TINYINT(1) DEFAULT '0' NOT NULL COMMENT 'zda se jedna o velkou ikonku' AFTER `meiprocode`;
ALTER TABLE `menuindexs` CHANGE `meipagid` `meipagid` INT(11) DEFAULT '0' NULL  COMMENT 'ID zaznamu',     CHANGE `meicatid` `meicatid` INT(11) DEFAULT '0' NULL ;

-- master categorie do URL
ALTER TABLE `catalogs` ADD COLUMN `catkeymaster` VARCHAR(255) NULL COMMENT 'URL klic master kategorie' AFTER `catkey`; 
ALTER TABLE `products` ADD COLUMN `prokeymaster` VARCHAR(255) NULL COMMENT 'URL klic nadrizene kat.' AFTER `prokey`; 
-- naplnit prokeymaster
-- http://www.dvaptaci.cz/administrace/import/gen-pro-key-master

create table requests
(
	reqid int auto_increment,
	reqfirname varchar(255) null,
	reqname varchar(255) null,
	reqmail varchar(255) null,
	reqphone varchar(255) null,
	reqnote text null,
	reqnoteint text null,
	reqdatec datetime null,
	reqdateu datetime null,
	constraint requests_pk
		primary key (reqid)
)
comment 'poptávky';

alter table requests add reqproid int not null after reqid;
alter table requests modify reqmail varchar(255) not null;
alter table requests add reqdelid int null after reqproid;

-- prod

