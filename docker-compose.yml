services:
  web:
    build:
      context: .docker
      dockerfile: Dockerfile
    volumes:
      - ./:/app
    ports:
      - 80:80
      - 443:443
    environment:
      PHP_IDE_CONFIG: serverName=ustikyfm.cz
      WEB_DOCUMENT_ROOT: /app/www
    links:
      - database

  database:
    image: mariadb:10.5.15
    volumes:
      - ./.docker/database:/docker-entrypoint-initdb.d:cached,ro
    ports:
      - 3308:3306
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: ustikyfmcz
      MYSQL_PASSWORD: ustikyfmcz
      MYSQL_DATABASE: ustikyfmcz1
    command: mysqld --sql_mode="NO_ENGINE_SUBSTITUTION"
