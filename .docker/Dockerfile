FROM webdevops/php-apache:5.6

# Instalace PHP extenzí
RUN docker-php-ext-install mysql

# Nastavení PHP
RUN echo "session.save_path = /tmp" >> /opt/docker/etc/php/php.ini \
    && echo "session.cookie_secure = 0" >> /opt/docker/etc/php/php.ini \
    && echo "session.cookie_lifetime = 1209600" >> /opt/docker/etc/php/php.ini \
    && echo "upload_max_filesize = 100M" >> /opt/docker/etc/php/php.ini \
    && echo "post_max_size = 100M" >> /opt/docker/etc/php/php.ini \
    && echo "memory_limit = 2048M" >> /opt/docker/etc/php/php.ini \
    && echo "max_execution_time = 240" >> /opt/docker/etc/php/php.ini

# Nastavení DocumentRoot pro Apache
ENV WEB_DOCUMENT_ROOT=/app/www

# Nastavení Xdebug
RUN echo "xdebug.remote_enable = 1" >> /opt/docker/etc/php/php.ini \
    && echo "xdebug.remote_host = host.docker.internal" >> /opt/docker/etc/php/php.ini \
    && echo "xdebug.remote_autostart = 1" >> /opt/docker/etc/php/php.ini
