.PHONY: up down restart logs xdebug-toggle bash clear-cache clear-cache-url help

# V<PERSON><PERSON><PERSON><PERSON> cíl při spu<PERSON> make bez parametrů
.DEFAULT_GOAL := help

# Barvy pro výstup
YELLOW := \033[1;33m
GREEN := \033[0;32m
NC := \033[0m # No Color

# Proměnné
DOCKER_COMPOSE = docker-compose
WEB_CONTAINER = web
DB_CONTAINER = database

help: ## Zobrazí nápovědu
	@echo "${YELLOW}Dostupné příkazy:${NC}"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "${GREEN}%-20s${NC} %s\n", $$1, $$2}'

up: ## Spustí všechny Docker kontejnery
	$(DOCKER_COMPOSE) up -d

down: ## Zastaví všechny Docker kontejnery
	$(DOCKER_COMPOSE) down

restart: ## Restartuje všechny Docker kontejnery
	$(DOCKER_COMPOSE) restart

logs: ## Zobrazí logy ze všech kontejnerů
	$(DOCKER_COMPOSE) logs -f

logs-web: ## Zobrazí logy z web kontejneru
	$(DOCKER_COMPOSE) logs -f $(WEB_CONTAINER)

logs-db: ## Zobrazí logy z databázového kontejneru
	$(DOCKER_COMPOSE) logs -f $(DB_CONTAINER)

xdebug-toggle: ## Přepne mezi verzí s Xdebug a bez něj
	./.docker/docker-xdebug.sh

bash: ## Spustí bash v web kontejneru
	$(DOCKER_COMPOSE) exec $(WEB_CONTAINER) bash

bash-db: ## Spustí bash v databázovém kontejneru
	$(DOCKER_COMPOSE) exec $(DB_CONTAINER) bash

mysql: ## Připojí se k MySQL/MariaDB konzoli
	$(DOCKER_COMPOSE) exec $(DB_CONTAINER) mysql -u ustikyfmcz -pustikyfmcz ustikyfmcz1

build: ## Znovu sestaví Docker image
	$(DOCKER_COMPOSE) build

clear-cache: ## Vymaže cache aplikace
	$(DOCKER_COMPOSE) exec $(WEB_CONTAINER) rm -rf /app/home/<USER>/cache/*
	$(DOCKER_COMPOSE) exec $(WEB_CONTAINER) rm -f /app/home/<USER>/btfj.dat
	@echo "Cache byla vymazána"

ps: ## Zobrazí běžící kontejnery
	$(DOCKER_COMPOSE) ps

clear-cache-url: ## Vymaže cache pomocí URL
	curl http://localhost/cc.php?k=lZwJIL
