# deploy master větve pomocí git-ftp https://github.com/git-ftp/git-ftp/blob/master/man/git-ftp.1.md
# vyžaduje nastavit v Settings -> Pipelines -> Environment variables pro FTP
# pokud ftp server podporuje používat pro FTP_..._URL ftpes:// protokol, tedy ftpes://ftp.server.name
# pro první spuštění a inicializaci na ftp serveru použít místo git ftp init ... nebo na server nahrát soubor .git-ftp.log ve kterém je ID aktuálního commitu, který je na FTP

image: samueldebruyn/debian-git

pipelines:
  branches:
    master:
      - step:
          script:
            - apt-get update
            - apt-get -qq install git-ftp
            - git config git-ftp.deployedsha1file home/.git-ftp.log
            - git ftp push --insecure --user $FTP_MASTER_USERNAME --passwd $FTP_MASTER_PASSWORD $FTP_MASTER_URL
            - curl -O https://www.dvaptaci.cz/cc.php?k=lZwJIL