<?php
/**
 * vym<PERSON><PERSON><PERSON> ad<PERSON> včetně obsahu
 *
 * @param $dir
 */
function rrmdir($dir, $deleteDir = TRUE) {
  if (is_dir($dir)) {
    $objects = scandir($dir);
    foreach ($objects as $object) {
      if ($object != "." && $object != "..") {
        if (filetype($dir . "/" . $object) == "dir") {
          rrmdir($dir . "/" . $object);
        } else {
          unlink($dir . "/" . $object);
        }
      }
    }
    reset($objects);
    if ($deleteDir) {
      rmdir($dir);
    }
  }
}

if ($_GET["k"] === 'lZwJIL') {
  rrmdir(__DIR__ . "/../home/<USER>/cache/", FALSE);
  @unlink(__DIR__ . "/../home/<USER>/btfj.dat");
  echo "Cache vymazana ...";
}
