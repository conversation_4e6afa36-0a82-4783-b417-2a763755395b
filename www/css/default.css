html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0
}
li {
	display: list-item;
	list-style:none
}
a img {
	border: none;
}
body {
	font-family: Arial, Helvetica, sans-serif;
	background: #fff url("../img/body.gif");
	color: #000;
	font-size: small;
	line-height: 1.5;
	text-align:center;
}
@media (min-width: 641px) {
	body {
		min-width: 980px;
	}
}
html, body {
	height: 100%;
}
.noscreen {
	width: 200px;
	position: absolute;
	left: 0;
	top: -1000px
}
hr {
	display: none;
}
.end {
	clear: both;
	height: 1px;
	line-height: 1px;
	font-size: 1px;
	display: block;
	visibility: hidden
}
body {

}
/* layout */
#back {
	width: 100%;
	min-height:100%;
	position:relative;
	background: url("../img/back.png") repeat-y center top

}

#main, #foot, #head, #menu {
	width: 100%;
	margin: 0 auto;
	text-align:left;
}
#menu {
	overflow: hidden;
}
@media (min-width: 641px) {
	#main, #foot, #head, #menu {
		width: 980px;
		overflow:hidden;
	}
}

#main {
	position:relative;
	background-color: white;
}
@media (min-width: 641px) {
	#main {
		background: url("../img/main.gif") repeat-y
	}
}
#in {
	width: 100%;
}
@media (min-width: 641px) {
	#in {
		overflow:hidden;
		width: 100%;
		background: url("../img/in.jpg") no-repeat;
	}
}

#head {
	height: 210px;
	overflow:hidden;
	position:relative;
	background: url("../img/head.jpg") no-repeat
}
@media (max-width: 640px) {
	#head {
		background: black url("../img/logo-mobile.jpg") center 10px no-repeat;
		height: auto;
		overflow: visible;
		padding-top: 90px;
		text-align: center;
	}
}


#content {
	position: relative;
}
@media (min-width: 641px) {
	#content {
		width: 735px;
		float: right;
		display:inline;
		margin-right: 10px;
		padding: 55px 0 15px 0;
	}
}
@media (max-width: 640px) {
	#content .in {
		padding: 10px;
	}
	#content img {
		max-width: 96%;
	}
}
#head .basket {
	width: 230px;
	position:absolute;
	right: 0;
	top: 7px;
	color:#fff;
	line-height: 1.2;
}
@media (max-width: 640px) {
	#head .basket {
		position: relative;
		width: 100%;
	}
}
#head .basket strong {
	color: #f6b51b;
	font-size: 120%

}
#head .basket a {
	display:block;
	width: 100px;
	height: 32px;
	line-height:32px;
	text-decoration:none;
	position:absolute;
	right: 5px;
	top: 75px;
	text-align:center;
	font-size: 110%;
	font-weight:bold
}
#head .basket a:link, #head .basket a:visited {
	color:#fff;
	background: url("../img/basket.png") left bottom;
}
#head .basket a:hover, #head .basket a:active, #head .basket a:focus {
	background-position: left top;
	color: #f6b51b;
}
@media (max-width: 640px) {
	#head .basket a {
		position: relative;
		right: auto;
		top: auto;
		margin: 5px auto 0 auto;
	}
	#head .basket h3 {
		display: none;
	}
}
#foot {
	padding: 15px 0;
	background:#000;
	text-align:center;
	border-top: 1px solid #c0c0c0;
	color: #f6b51b;
}
@media (max-width: 640px) {
	#foot {
		font-size: 12px;
	}
	#next img {
		max-width: 99%;
		margin: 0 auto;
	}
	#next div.next {
		border: 1px solid #d6d6d6;
		border-radius: 5px;
		width: 23%;
		margin: 0.5%;
		float: left;
		position: relative;
		text-align: center;
	}
	div.next span, div.next1 span {
		display: block;
		position: absolute;
		bottom: 0;
		background-color: #fbbf2d;
		z-index: 20;
		font-size: 11px;
		text-align: center;
		width: 100%;
		padding: 5px 0;
		height: 16px;
		overflow: hidden;
	}
}
@media (max-width: 419px) {
	#next div.next {
		width: 48%;
	}
}
@media (min-width: 641px) {
	#next {
		width: 100%;
		overflow:hidden;
		padding-bottom: 10px;
	}
	#next img {
		display: inline;
		float:left;
		margin: 0 0 15px 0
	}
	#next div.next {
		width: 171px;
		height: 159px;
		margin: 0 12px 6px 0;
		background: #f0f0f0;
		position:relative;
		left: 0;
		float:left;
		display:inline;
		background: url("../img/next.jpg") no-repeat left bottom;
	}
	#next div.next img {
		width: 140px;
		height: 110px;
		margin: 5px 15px 0 15px
	}
	div.next span, div.next1 span {
		display:block;
		text-align:center;
		width: 100%;
		height: 32px;
		line-height:32px;
		overflow:hidden;
		white-space:nowrap;
		z-index:5;
		position:absolute;
		bottom: 4px;
		left: 0;

	}
}
#next div.next1 {
	width: 352px;
	position:relative;
	height: 199px;
	display: inline;
	left: 0;
	margin: 0 14px 6px 0;
	float:left;
	background: url("../img/next1.jpg") no-repeat left bottom
}
#next div.next1 img {
	width: 282px;
	height: 125px;
	margin: 10px 0 0 30px;

}
#next div.next p, #next div.next1 p {
	padding: 0;
}
div.next br {
	display:none;
}
div.next1 span {
	bottom: 3px;
	font-size:16px;
}
div.next a, div.next1 a {
	text-decoration:none;
	font-weight:bold;
	font-size: 14px;
}
div.next a:link,div.next a:visited, div.next1 a:link, div.next1 a:visited {
	color:#000
}
div.next a:hover, div.next a:active, div.next a:focus, div.next1 a:hover, div.next1 a:active, div.next1 a:focus {
	color:#333
}
/* layout */

/* menu */
#menu {
	width: 640px;
	height: 40px;
	position:absolute;
	left: 0;
	bottom: 0;
}
#menu ul {
	width: 640px;
	overflow:hidden;
	height: 40px;
	position:relative;
	left: 10px;
	margin: 0;
	padding: 0
}
#menu li {
	height: 40px;
	display:inline;
	margin: 0;
	padding: 0;
	float:left;
	background: url("../img/menuli.gif") no-repeat right top;

}
#menu li a, #menu li strong {
	display: block;
	padding: 0 10px;
	height:40px;
	line-height: 40px;
	text-decoration:none;
	overflow:hidden;
	white-space:nowrap;
	text-align:center;
	font-weight:bold;
}
#menu li a:link, #menu li a:visited, #menu li strong{
	color:#000;
}
#menu li a:hover, #menu li a:active, #menu li a:focus {
	color:#fff;
	background:#000
}
#menu li strong {
	color:#fff;
	background:#f6a908
}
#supersized-loader {
	display:none
}
@media (max-width: 640px) {
	#menu {
		position: relative;
		width: 100%;
		height: auto;
		background-color: #e2ac32;
	}
	#menu ul {
		width: auto;
		left: 0;
	}
	#menu li a, #menu li a:link, #menu li a:visited, #menu li strong {
		font-size: 12px;
	}
}
@media (max-width: 399px) {
	#menu li:last-child {
		display: none;
	}
	#menu li a, #menu li a:link, #menu li a:visited, #menu li strong {
		font-size: 10px;
	}
}

/* menu */

/* headers */
h1, h1 span {
	color:#000;
	width: 359px;
	height: 170px;
	left: 0;
	top: 0;
	display:block;
	position:absolute;
	cursor: pointer;
	cursor:hand;
	z-index:1
}
h1 span {
	background: url("../img/logo.jpg") no-repeat;
	z-index:2
}
@media (max-width: 640px) {
	h1 {
		width: 100%;
		height: 85px;
		overflow: hidden;
		text-indent: -9999999px;
	}
	h1 span {
		background: none;
	}
}
h2, h2 span {
	width: 352px;
	height: 51px;
	position: absolute;
	display:block;
	overflow:hidden;
	z-index: 3;
	top: 0;
	left: 154px;

}
h2 span {
	background: url("../img/h2.jpg") no-repeat;
	left: 0;
	top: 0;
	z-index:4
}
@media (max-width: 640px) {
	h2 {
		display: none;
	}
}

#nav h3 {
	font-size: 150%;
	color: #f6b51b;
	padding: 0 0 1px 10px;
	font-weight:normal
}
#nav h3.other {
	color: #fff;
	padding: 5px 10px 5px 20px;
	background:#444;
	margin: 0 -10px 10px -10px ;
	font-size: 130%
}
/* headers */
/* crumb */
.crumb {
	width: 100%;
	font-size: 110%;
	padding: 0 0 10px 0;
	background: url("../img/border.gif") repeat-x left bottom

}
#content h3 {
	color:#dfa009;
	font-size: 140%;
	font-weight: normal;
	padding: 0 0 10px 0
}
#content #detail h3 {
	padding-right: 200px;
}
#content .crumb h4 {
	font-weight: normal
}
/* crumb */
/* detail */
#detail {
	width: 100%;
	overflow:hidden;
	clear:left;
	overflow: hidden;
	padding-top: 20px;
	position:relative
}
p.detail {
	display: inline;
	float:right;
	margin: 0 0 20p 20px
}
#content #detail h3.desc {
	clear:both;
	width: 100%;
	padding: 15px 0;
	background: url("../img/border.gif") repeat-x;
	margin-top: 10px;
}

#content div.lists ul, #content ul {
	padding: 0 0 15px 15px;
	margin: 0;
}
#detail .lists li,  ul li {
	background: url("../img/lili.gif") no-repeat 0 0.55em;
	margin: 0 0 5px 0;
	padding: 0 0 0 15px
}
ul li {
	background: url("../img/textli.gif") no-repeat 0 0.55em;
}
#detail .lists li.price {
	background: #b31316;
	color:#fff;
	max-width: 220px;
	padding: 5px 10px;
	margin: 10px 0 10px 0;
}
#detail .lists li.price span  {
	font-weight: normal;
	font-size: 80%;
}
#detail .lists li.owner {
	float:left;
	display:inline;
	background:none;
	width: 160px;
	padding: 5px 0;
	background:#318b31;
	font-size: 130%;
	color:#fff;
	position: absolute;
	text-align:center;
	right: 0;
	top: 10px;
	border-radius: 5px ;
	-moz-border-radius: 5px
}
img.imgl {
	display: inline;
	float:left;
	margin: 0 15px 15px 0
}
img.imgr {
	display: inline;
	float:right;
	margin: 0 0 15px 15px
}
/* detail */
/* navigation */
#nav {
	width: 220px;
	float:left;
	display:inline;
}
#nav .in {
	zoom: 1;
	overflow:hidden;
	padding: 10px;
}
#nav ul {
	zoom:1;
	overflow:hidden;
	padding: 0 0 20px 0
}


#nav li {
	width: 100%;
	float:left;
	display:inline;
	margin:  0 0 3px 0;
	background:none;
	padding: 0
}
#nav li a, #nav li strong {
	display:block;
	height: 28px;
	zoom: 1;
	padding: 0 0 0 10px;
	line-height:28px;
	text-decoration:none;
	color:#fff;
	font-weight:normal
}
#nav li a strong {
	margin-left: -10px;

}
#nav li a:link, #nav li a:visited, #nav li strong {
	background: url("../img/nav.png") no-repeat left bottom;
}
#nav li a:hover, #nav li a:active, #nav li a:focus, #nav li  strong {
	color:#000;
	background-position: left top
}
#nav li ul {
	padding: 5px 0 2px 0;
}
#nav li ul li {
	margin: 0;
	border-bottom: 1px solid #333
}
#nav li ul li ul {
	font-size: 90%;
	padding: 5px 0 2px 10px
}
#nav li ul li a, #nav li ul li strong, #nav .userform a {
	height: auto;
	line-height: 1.5;
	padding: 5px 10px 5px 20px;

}
#nav .userform a {
	line-height: 1.2;
	font-size: 105%
}
#nav .userform p {
	text-align:left;
	padding-left: 5px;
	padding-top: 10px;
}
#nav li ul li a strong {
	margin-left: -20px;
	padding-top: 0;
	padding-bottom: 0
}
#nav li ul li a:link, #nav li ul li a:visited, #nav li ul li strong, #nav .userform a:link, #nav .userform a:visited {
	color: #f6b51b;
	background: url("../img/lili.gif") no-repeat 10px 0.75em

}
#nav li ul li strong {

}
#nav li ul li a:hover, #nav li ul li a:active, #nav li ul li a:focus, #nav .userform a:hover, #nav .userform a:active, #nav .userform a:focus  {
	color:#fff;
	background-position: -210px 0.75em

}
#nav li ul li a.active:link,#nav li ul li a.active:visited,#nav li ul li a.active:hover,#nav li ul li a.active:active,#nav li ul li a.active:focus  {
	background:none
}
#nav li ul li ul li ul {
	background: #333;
	padding-left: 5px;
	margin: 3px 0 3px 10px;
}
#nav li ul li ul li {
	border-bottom: none;
	border-top: 1px solid #333
}
#nav li ul li ul li ul li a:link, #nav li ul li ul li ul li a:visited {
	color: #fff;
	background: url("../img/lili.gif") no-repeat -210px 0.75em;
	line-height:1.5
}
#nav li ul li a strong {
background-position: -430px 0.45em;
}
#nav li ul li ul li ul li strong {
	line-height:1.5;
	background-position: -430px 0.55em;
}
#nav li ul li ul li ul li a:hover, #nav li ul li ul li ul li a:active, #nav li ul li ul li ul li a:focus {
	color: #f6b51b;
	background: url("../img/lili.gif") no-repeat 10px 0.75em
}
#nav li ul li strong {
	color:#c0c0c0;
	background-position: -430px 0.75em;
	font-weight:bold
}
#nav p a:link, #nav p a:visited, #foot a:link, #foot a:visited {
	color: #f6b51b;
}
#nav p a:hover, #nav p a:active, #nav p a:focus, #foot a:hover, #foot a:active, #foot a:focus {
	color: #fff;
}
.nav__button {
	display: none;
	padding: 10px 10px 5px 10px;
}
.nav__button a {
	color: black !important;
	background: #f6b51b url("../img/arrow.png") 10px center no-repeat;
	font-weight: bold;
	border-radius: 5px;
	text-decoration: none;
	padding: 8px 15px 7px 35px;
	font-size: 17px;
	text-transform: uppercase;
}
.nav__close {
	color: white;
	position: absolute;
	top: 5px;
	right: 15px;
	font-size: 25px;
	text-transform: uppercase;
	font-weight: bold;
	display: none;
}
@media (max-width: 640px) {
	#nav {
		background-color: black;
		width: 100%;
	}
	#nav a {
		max-width: 200px;
	}
	.nav__button, .nav__close {
		display: block;
	}
}
/* navigation */
/* flash info  */
#content div.flash, #content ul.error {
	padding: 10px 15px;
	border-bottom: 10px solid #fff;
}
#content div.info {
	background: #25714b;
	color: #fff;
}
#content div.err, #content ul.error {
	background: #840000;
	color: #fff
}
#content ul.error {
	padding-bottom: 5px
}
div.submit {
	padding: 20px 0
}
#content fieldset {
	padding: 0 20px 15px;
}
#frmorderContactForm-shipto {
	margin-right: 10px;
}
textarea {
	width: 80%;
}
/* flash info  */

/* table */
.table, table {
	width:100%;
	font-size:100%;

}
th, td {
	padding: 7px 10px;
	border: 1px solid #c0c0c0
}
.proparams th {
	background: #f0f0f0;
	width: 150px;
}
.basket th, .basket td  {
	border-left: none;
	border-right: none;
}

.basket th {
	background: #f0f0f0
}
.basket tfoot td {
	font-weight: bold;
	font-size:105%;
	color:#840000
}
div.submit input.button, #userform table input.button {
	padding: 5px 15px;
	margin: 0 10px;
}
#userform table input.button {
	margin: 0
}
div.submit {
	text-align:center
}
.basket tbody th, .basket tbody td {
	border:none
}
.userform table tbody td, .userform table tbody th, #userform table tbody td, #userform table tbody th, #contactform table tbody th, #contactform table tbody td {
	border:none;

}
#contactform table tbody th, #contactform table tbody td {
	background: #f2f2f2;
	padding: 10px 15px;
}
label {
	padding: 0 0 5px 0;
}
.required {
	color:#840000
}
.basket input, .basket select {
	margin-top: 5px;
	margin-bottom: 5px;
}
div.adresa td {
	border:none
}
fieldset {
	border: 1px solid #d0d0d0
}
#content .news table thead th {
	background: #e2f3fe;
}
#content .news table tbody th {
	vertical-align:top
}
legend {
	margin: 0 20px;
	padding: 10px;
	font-size: 130%;
	color:#69a417;

}
@media (max-width: 640px) {
	fieldset input {
		max-width: 230px;
	}
}
/* table */


/* form */
#nav .userform {
	width: 100%;
	text-align:center;
	padding: 10px 0
}
@media (max-width: 640px) {
	#nav .userform {
		width: 149px;
	}
}
#nav .userform br {
	display:none
}
#nav .userform input {

	width: 80%;
	display:block;
	margin: 0 auto 10px auto
}
#nav .userform input#frmuserLoginForm-submit {
	width: 149px;
	height: 32px;
	line-height:32px;
	background: url("../img/button.png");
	border: none;
	color:#fff
}
#nav .userform input#frmuserLoginForm-submit:hover {
	background-position: left bottom
}
#nav .userform p a {
	display:block;
}
#search{
	width: 268px;
	height: 40px;
	position:absolute;
	right: 0;
	bottom: 0;
	background:url("../img/search.jpg") no-repeat;
}
#search form {
	width: 268px;
	height: 40px;
	position:relative
}
#search #frmsearchForm-name {
	width: 150px;
	position:relative;
	top: 9px;
	left: 30px;
	background: none;
	color:#000;
	border: none
}
#search input.button {
	width: 80px;
	height: 40px;
	position:absolute;
	left: 180px;
	top: 0;
	border: none;
	background:none;
	text-align:center;
	font-weight:bold;
	color:#000
}
input.button {
	cursor:pointer;
	cursor:hand;
}
@media (max-width: 640px) {
	#search {
		width: 100%;
		position: relative;
		background: #e2ac32;
	}
	#search form {
		margin: 0 auto;
		background:url("../img/search-mobile.jpg") center top no-repeat;
		position: relative;
	}
	#search #frmsearchForm-name {
		position: absolute;
		width: 145px;
		top: 10px;
		left: 30px;
	}
}

/* form */


/* paragraphs */
p {
	padding: 0 0 15px 0
}



.paginator {
	width: 100%;
	clear:both;
	padding: 10px 0;
	overflow:hidden;
}
#content .paginator p {
	display: inline;
	float:right;
}
.paginator a, .paginator span {
	display:inline;
	float:left;
	padding: 5px 10px;
	background:#000;
	border: 1px solid #000;
	color:#fff;
	margin: 0 3px;
	border-radius: 5px ;
	-moz-border-radius: 5px
}
.paginator span.current {
	color: #000;
	font-weight: bold;
	font-size: 105%;
	background: #f0f0f0;
	border: 1px solid #dedede
}
.paginator a:link,.paginator a:visited {
	color: #fff;
	text-decoration:none;
	background:#000 ;
}
.paginator a:hover, .paginator a:active, .paginator a:focus {
	background:#f6b51b ;
	color: #333;
	border-color:#f6b51b
}
#content .address {
	width: 50%;
	display:inline;
	float:left;
	padding: 0 0 20px 0
}
#content address {
	font-style:normal;
}
@media (max-width: 640px) {
	#content .address {
		width: 100%;
		display: block;
		float: none;
		padding: 0;
	}
}
#content p.cleft {
	clear:left;
	border-bottom: 1px solid #c0c0c0;
	margin-bottom: 20px
}
/* paragraphs  */
/* lists */



/* lists */
/* table */

/* table */
/* catalog */
h4 {
	font-size: 105%;
	padding: 0 0 5px 0;
}

#kat {
	width: 100%;
	overflow:hidden	;
	border-bottom: 15px solid #fff;
	clear:both
}
#kat .kat h4 {
	width: 100%;
	font-size: 100%;
	padding: 0;
	font-weight:normal

}
#kat h4 a {
	display: block;
	text-decoration: none;
	overflow: hidden;
	padding: 5px 10px;
	height: 50px;
	overflow: hidden;
}
#kat h4 a:link, #kat h4 a:visited {
	color:#fff;
	background:#000
}
#kat h4 a:hover, #kat h4 a:active, #kat h4 a:focus {
	color: #000;
	background:#f6b51b;
}
.kat {
	width: 183px;
	float:left;
	font-size: 90%;
}
@media (max-width: 419px) {
	.kat {
		width: 98%;
		margin: 1% 0.5%;
		float: none;
	}
	.kat .in {
		margin: 0 !important;
	}
}
@media (min-width: 420px) and (max-width: 640px) {
	.kat {
		width: 24%;
		margin: 1% 0.5%;
	}
	.kat .in {
		margin: 0 !important;
	}
}
.kat input {
	width: 25px;
	float:right;
	margin-right: 10px;
	text-align:center;
	padding: 3px 0;
	font-weight:bold;
	border: 1px solid #d2d2d2

}
.kat span {
	display:block;
	width: 100%;
	text-align:center
}
.kat img, .catimg img, #nextimg img {
	display:block;
	margin: 5px auto;
	padding: 5px;
	border: 1px solid #d0d0d0;
	border-radius: 5px ;
	-moz-border-radius: 5px
}
#nextimg img {
	margin: 0
}
.catimg img {
	float:left;
	margin: 0 10px 10px 0;
	width: 120px;

}
p.detail img {
	border: 1px solid #d0d0d0;

}
p.detail a img {
	cursor: url("../highslide/graphics/zoomin.cur"), pointer
}
.kat a:hover img, p.detail a:hover img, #nextimg a:hover img {
	border-color:#f6b51b;
}
.kat .in {
	zoom: 1;
	overflow:hidden;
	margin: 0 10px 5px 0 ;
	border: 1px solid #c0c0c0;
	padding-bottom: 5px;
	border-radius: 5px ;
	-moz-border-radius: 5px

}
#katin {
	width: 100%;
	overflow:hidden;

}
#kat p.price {
	background: #fff
}
#kat p.price input.button {
	font-size:100%;
	padding: 5px 0;
	border-color:#fff

}
#kat p {
	background: #dfdfdf;
	padding: 5px 10px;
	height: 50px;
	font-size:11px
}
#kat p.price strong {
	display:block;
	padding:0 0 5px 0;
	font-size: 110%;
	text-align:center;
	color:#e68a00
}
.lists .price {
	font-size: 120%;
	color:#e68a00;
	border-radius: 5px ;
	-moz-border-radius: 5px
}
#kat p.price a {
	display:inline;
	float:right;
	padding: 4px 7px;
	text-decoration:none;
	text-align:center;
	font-weight:bold;
	border-radius: 5px ;
	-moz-border-radius: 5px

}

#kat p.price a.adetail {
	float:left;
}
#detail p.addbasket {
	display:inline-block;
	margin: 10px 0 0 15px;
	border-radius: 5px ;
	-moz-border-radius: 5px
}
#detail p.addbasket a {
	display:inline;
	float:left;
	padding: 10px 20px;
	text-decoration:none;
	font-weight:bold;
	border-radius: 5px ;
	-moz-border-radius: 5px

}
a.adbasket:link, a.adbasket:visited,#detail p.addbasket a:link, #detail p.addbasket a:visited   {
	color:#000;
	background:#f6b51b ;
}
 a.adbasket:hover,  a.addasket:active,  a.adbasket:focus, #detail p.addbasket a:hover, #detail p.addbasket a:active, #detail p.addbasket a:focus {
	color:#fff;
	background:#000
}
#kat p.price a.adetail:link, #kat p.price a.adetail:visited {
	background:#dfdfdf;
	color:#000
}
#kat p.price a.adetail:hover, #kat p.price a.adetail:active,#kat p.price a.adetail:focus {
	color:#fff;
	background:#000
}
#salestat, div.proparams {
	padding: 0 0 20px 0
}
#nextimg{
	width: 100%;
	overflow:hidden;
}
#nextimg p {
	display:inline;
	float:left;
	margin: 0 20px 20px 0
}
div.attach {
	padding: 20px 0;
	background: url("../img/border.gif") repeat-x

}
div.attach, ul.attach li {
	width: 100%;
	overflow:hidden;
}
ul.attach li {
	margin: 0 0 5px 0
}
#content div.attach ul {
	padding-left: 20px;
}
div.attach li img {
	display:inline;
	float:left;
	margin: 0 10px 0 0;
}
/* catalog */
/* basket, login form */
#new .basket, #new .userform, #new .actual {
	text-align:center;
	background:#f0f0f0;
	border-bottom: 10px solid #fff
}
#new .basket p, #new .userform form {
	padding: 15px 0
}
#new .basket strong, .kat p.price strong {
	color:#0b9bec
}
#new .basket a, #new .userform input.button   {
	display:block;
	width: 130px;
	height: 34px;
	line-height:34px;
	text-decoration:none;
	white-space:nowrap;
	overflow:hidden;
	background: #023f6c url("../img/button.jpg") no-repeat right top;
	margin: 10px auto 0 auto;
	border:none;
	color:#fff;
}


#new .basket a:link, #new .basket a:visited {
	color:#fff;
}
#new .basket a:hover, #new .basket a:active, #new .basket a:focus {
	color:#fefefe;
	background-position: left top
}
#new .userform input {
	width: 80%;
	margin: 0 auto 10px auto
}
#new .userform input.button:hover, input#frmbasketForm-makeorder:hover {
	background-position: left top

}
input#frmmakeOrderForm-shipto {
	margin-right: 10px;
}
b.color, b.color1, strong.color, strong.color1 {
	font-weight: normal
}
b.color, strong.color {
	color:#16aa16;
}
b.color1,strong.color1 {
	color:#8a30b1
}
b.color2,strong.color2 {
	color:#d30d11
}
/* basket, login form */


/* table */
.table {
	padding: 0 0 20px 0;
	width: 100%;
}
#content div.table table.text td {
	text-align:center;
	vertical-align:top	!important
}
caption {
	font-weight: bold;
	text-align:center;
	padding: 10px 0;
	font-size: 105%
}
table {
	border-collapse:collapse;
	width: 100%;
	font-size:100%;
}
th, td {
	text-align:left;
	padding: 5px 10px;
}
.info td {
	text-align:center
}
/* table */
/* info */
div#information {
	padding: 10px 0;
	width:100%
}
span.red {
	color:#840000;
}
span.blue {
	color:#04538c
}
span.green {
	color:#008000
}
#english {
	width: 100%;
	padding: 0 0 10px 0;
}
#english img {
	vertical-align:middle;
	margin-right: 10px;
}
div.reclink {
	zoom: 1;
	padding: 15px 15px 5px 15px;
	border: 1px solid #d0d0d0;
	margin-bottom: 20px;
}
#content div.reclink p {
	padding: 0 0 10px 0
}
#content div.reclink h4 {
	padding: 0 0 5px;
	font-size:105%;
	color:#f60
}
/* info */
/* highslide */
.highslide-container div {
	font-size: 10pt
}
.highslide {
	outline: none;
	text-decoration: none
}
.highslide-gallery .highslide-active-anchor img {
	border-color: black;
	visibility: visible;
	cursor: default
}
.highslide-image {
	border: none;
	background: #f6f1ea
}
.highslide-wrapper, .highslide-outline {
	background: white
}
.highslide-number {
	font-weight: bold;
	color: gray
}
.highslide-heading {
	display: none;
	font-weight: bold;
	margin: 0.4em
}
.highslide-dimming {
	position: absolute;
	background: #14262c
}
a.highslide-full-expand {
	background: url("../highslide/graphics/fullexpand.gif") no-repeat;
	display: block;
	margin: 0 10px 10px 0;
	width: 34px;
	height: 34px
}
.highslide-loading {
	display: block;
	color: black;
	font-size: 9px;
	font-weight: bold;
	text-transform: uppercase;
	text-decoration: none;
	padding: 3px;
	border: 1px solid white;
	background-color: white;
	padding-left: 22px;
	background-image: url("../highslide/graphics/loader.white.gif");
	background-repeat: no-repeat;
	background-position: 3px 1px
}
a.highslide-loading:link {
	text-decoration: none;
	color: #000
}
.highslide-move, .highslide-move * {
	cursor: move
}
.highslide-overlay {
	display: none
}
.closebutton {
	position: relative;
	top: -15px;
	left: 15px;
	width: 30px;
	height: 30px;
	cursor: pointer;
	background: url("../highslide/graphics/close.png")
}
.highslide-controls {
	position: relative;
	width: 195px;
	height: 40px;
	background: url("../highslide/graphics/controlbar-white.gif") 0 -90px no-repeat;
	margin: 20px 15px 10px 0
}
.highslide-controls ul {
	position: relative;
	left: 15px;
	height: 40px;
	list-style: none;
	margin: 0;
	padding: 0;
	background: url("../highslide/graphics/controlbar-white.gif") right -90px no-repeat
}
.highslide-controls li {
	float: left;
	padding: 5px 0
}
.highslide-controls a {
	background-image: url("../highslide/graphics/controlbar-white.gif");
	display: block;
	float: left;
	height: 30px;
	width: 30px;
	outline: none
}
.highslide-controls a.disabled {
	cursor: default
}
.highslide-controls a span {
	display: none
}
.highslide-controls .highslide-previous a {
	background-position: 0 0
}
.highslide-controls .highslide-previous a:hover {
	background-position: 0 -30px
}
.highslide-controls .highslide-previous a.disabled {
	background-position: 0 -60px !important
}
.highslide-controls .highslide-play a {
	background-position: -30px 0
}
.highslide-controls .highslide-play a:hover {
	background-position: -30px -30px
}
.highslide-controls .highslide-play a.disabled {
	background-position: -30px -60px !important
}
.highslide-controls .highslide-pause a {
	background-position: -60px 0
}
.highslide-controls .highslide-pause a:hover {
	background-position: -60px -30px
}
.highslide-controls .highslide-next a {
	background-position: -90px 0
}
.highslide-controls .highslide-next a:hover {
	background-position: -90px -30px
}
.highslide-controls .highslide-next a.disabled {
	background-position: -90px -60px !important
}
.highslide-controls .highslide-move a {
	background-position: -120px 0
}
.highslide-controls .highslide-move a:hover {
	background-position: -120px -30px
}
.highslide-controls .highslide-full-expand a {
	background-position: -150px 0
}
.highslide-controls .highslide-full-expand a:hover {
	background-position: -150px -30px
}
.highslide-controls .highslide-full-expand a.disabled {
	background-position: -150px -60px !important
}
.highslide-controls .highslide-close a {
	background-position: -180px 0
}
.highslide-controls .highslide-close a:hover {
	background-position: -180px -30px
}
.draggable-header .highslide-header {
	height: 16px
}
.draggable-header .highslide-header .highslide-move {
	cursor: move;
	display: block;
	height: 16px;
	position: absolute;
	left: 0;
	right: 16px;
	top: 0;
	width: auto;
	z-index: 1
}
.draggable-header .highslide-header .highslide-move * {
	display: none
}
.draggable-header .highslide-header .highslide-close {
	position: relative;
	float: right;
	z-index: 2;
	padding: 0
}
.draggable-header .highslide-header .highslide-close a {
	display: block;
	height: 16px;
	width: 16px;
	background-image: url("../highslide/graphics/closeX.png")
}
.draggable-header .highslide-header .highslide-close a:hover {
	background-position: 0 16px
}
.draggable-header .highslide-header .highslide-close span {
	display: none
}
.no-footer .highslide-footer {
	display: none
}
.wide-border .highslide-image {
	border-width: 20px
}
.wide-border .highslide-caption {
	padding: 0 10px 10px 10px
}
.borderless .highslide-image {
	border: none
}
.borderless .highslide-caption {
	border-bottom: 1px solid white;
	border-top: 1px solid white;
	background: silver
}
.outer-glow {
	background: #444
}
.outer-glow .highslide-image {
	border: 5px solid #444444
}
.outer-glow .highslide-caption {
	border: 5px solid #444444;
	border-top: none;
	padding: 5px;
	background-color: gray
}
.colored-border .highslide-image {
	border: 2px solid green
}
.colored-border .highslide-caption {
	border: 2px solid green;
	border-top: none
}
.floating-caption .highslide-caption {
	position: absolute;
	padding: 1em 0 0 0;
	background: none;
	color: white;
	border: none;
	font-weight: bold
}
.controls-in-heading .highslide-heading {
	color: gray;
	font-weight: bold;
	height: 20px;
	overflow: hidden;
	cursor: default;
	padding: 0 0 0 22px;
	margin: 0;
	background: url("../highslide/graphics/icon.gif") no-repeat 0 1px
}
.controls-in-heading .highslide-controls {
	width: 105px;
	height: 20px;
	position: relative;
	margin: 0;
	top: -23px;
	left: 7px;
	background: none
}
.controls-in-heading .highslide-controls ul {
	position: static;
	height: 20px;
	background: none
}
.controls-in-heading .highslide-controls li {
	padding: 0
}
.controls-in-heading .highslide-controls a {
	background-image: url("../highslide/graphics/controlbar-white-small.gif");
	height: 20px;
	width: 20px
}
.controls-in-heading .highslide-controls .highslide-move {
	display: none
}
.controls-in-heading .highslide-controls .highslide-previous a {
	background-position: 0 0
}
.controls-in-heading .highslide-controls .highslide-previous a:hover {
	background-position: 0 -20px
}
.controls-in-heading .highslide-controls .highslide-previous a.disabled {
	background-position: 0 -40px !important
}
.controls-in-heading .highslide-controls .highslide-play a {
	background-position: -20px 0
}
.controls-in-heading .highslide-controls .highslide-play a:hover {
	background-position: -20px -20px
}
.controls-in-heading .highslide-controls .highslide-play a.disabled {
	background-position: -20px -40px !important
}
.controls-in-heading .highslide-controls .highslide-pause a {
	background-position: -40px 0
}
.controls-in-heading .highslide-controls .highslide-pause a:hover {
	background-position: -40px -20px
}
.controls-in-heading .highslide-controls .highslide-next a {
	background-position: -60px 0
}
.controls-in-heading .highslide-controls .highslide-next a:hover {
	background-position: -60px -20px
}
.controls-in-heading .highslide-controls .highslide-next a.disabled {
	background-position: -60px -40px !important
}
.controls-in-heading .highslide-controls .highslide-full-expand a {
	background-position: -100px 0
}
.controls-in-heading .highslide-controls .highslide-full-expand a:hover {
	background-position: -100px -20px
}
.controls-in-heading .highslide-controls .highslide-full-expand a.disabled {
	background-position: -100px -40px !important
}
.controls-in-heading .highslide-controls .highslide-close a {
	background-position: -120px 0
}
.controls-in-heading .highslide-controls .highslide-close a:hover {
	background-position: -120px -20px
}
a.highslide-credits:link, a.highslide-credits {
	text-decoration: none;
	display: block;
	height: 30px;
	overflow: hidden;
	padding: 0 20px 0 10px;
	background: url("../highslide/graphics/credits.png") no-repeat right bottom
}
#news span.highslide-caption, #content span.highslide-caption, #content .photos br {
	display: none
}
/* highlide */

/* links */
a:link, a:visited {
	color:#466f9d
}
a:hover, a:active, a:focus {
	color:#000;
}


/* links */
/*  Slider */

.coin-slider { display: none; width:730px;height:220px;border-bottom: 20px solid #fff; overflow: hidden; zoom: 1; position: relative; }
.coin-slider a{ text-decoration: none; outline: none; border: none; }
.cs-buttons { font-size: 0px; padding: 10px; float: left; }
.cs-buttons a { margin-left: 5px; height: 10px; width: 10px; float: left; border: 1px solid #B8C4CF; color: #B8C4CF; text-indent: -1000px; }
.cs-active { background-color: #B8C4CF; color: #FFFFFF; }
.cs-title { width: 730px;  background-color: #000000; color: #FFFFFF; }
.cs-prev,
.cs-next {display:none }
@media (min-width: 641px) {
	.coin-slider {
		display: block;
	}
}

.button a {
    background: #0B5B60;
    margin: 10px 100px;
    opacity: 1;
    outline: medium none;
    padding: 5px ;
    text-align: center;
    text-decoration: none;
    display: block;
    line-height: 50px;
    background: #599BDC;
    background: -webkit-linear-gradient(top,  #599BDC, #3072B3); /* webkit browsers */
    background:    -moz-linear-gradient(top,  #599BDC, #3072B3); /* firefox 3.6+ */
    background:      -o-linear-gradient(top,  #599BDC, #3072B3); /* opera */
    background:     -ms-linear-gradient(top,  #599BDC, #3072B3); /* IE10 */
    background:         linear-gradient(top,  #599BDC, #3072B3); /* css3 */
    border: 1px solid #518CC6;
    color: #FFFFFF;
    text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.3);
    border-radius: 5px;
}
.button a:hover {
    background: #3072B3;
    background: -webkit-linear-gradient(top,  #3072B3, #599BDC); /* webkit browsers */
    background:    -moz-linear-gradient(top,  #3072B3, #599BDC); /* firefox 3.6+ */
    background:      -o-linear-gradient(top,  #3072B3, #599BDC); /* opera */
    background:     -ms-linear-gradient(top,  #3072B3, #599BDC); /* IE10 */
    background:         linear-gradient(top,  #3072B3, #599BDC); /* css3 */
    box-shadow: 0 0 3px #518CC6;
}
.cs-buttons a {
    border-radius: 50%;
    background: #CCC;
    background: -webkit-linear-gradient(top,  #CCCCCC, #F3F3F3); /* webkit browsers */
    background:    -moz-linear-gradient(top,  #CCCCCC, #F3F3F3); /* firefox 3.6+ */
    background:      -o-linear-gradient(top,  #CCCCCC, #F3F3F3); /* opera */
    background:     -ms-linear-gradient(top,  #CCCCCC, #F3F3F3); /* IE10 */
    background:         linear-gradient(top,  #CCCCCC, #F3F3F3); /* css3 */
}
a.cs-active {
    background: #599BDC;
    background: -webkit-linear-gradient(top,  #599BDC, #3072B3); /* webkit browsers */
    background:    -moz-linear-gradient(top,  #599BDC, #3072B3); /* firefox 3.6+ */
    background:      -o-linear-gradient(top,  #599BDC, #3072B3); /* opera */
    background:     -ms-linear-gradient(top,  #599BDC, #3072B3); /* IE10 */
    background:         linear-gradient(top,  #599BDC, #3072B3); /* css3 */
    border-radius: 50%;
    box-shadow: 0 0 3px #518CC6;
    border: 1px solid #3072B3 !important;
}




/*  Slider */

#slider {
	width: 730px;
	height: 220px;
	overflow:hidden;
	border-bottom: 15px solid #fff
}

IMG {
	BORDER-BOTTOM: medium none; BORDER-LEFT: medium none; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
#supersized-loader {
	Z-INDEX: 10; POSITION: absolute; BACKGROUND-COLOR: #000; TEXT-INDENT: -999em; MARGIN: -30px 0px 0px -30px; WIDTH: 60px; HEIGHT: 60px; TOP: 50%; LEFT: 50%; -webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px
}
#supersized {
	Z-INDEX: -999; POSITION: fixed; WIDTH: 100%; HEIGHT: 100%; OVERFLOW: hidden; TOP: 0px; LEFT: 0px
}
#supersized IMG {
	BORDER-BOTTOM: medium none; POSITION: relative; BORDER-LEFT: medium none; OUTLINE-STYLE: none; OUTLINE-COLOR: invert; OUTLINE-WIDTH: medium; WIDTH: auto; DISPLAY: none; HEIGHT: auto; BORDER-TOP: medium none; BORDER-RIGHT: medium none
}
.speed#supersized IMG {
	-MS-INTERPOLATION-MODE: nearest-neighbor; image-rendering: -moz-crisp-edges
}
.quality#supersized IMG {
	-MS-INTERPOLATION-MODE: bicubic; image-rendering: optimizeQuality
}
#supersized A {
	Z-INDEX: -30; POSITION: absolute; WIDTH: 100%; BACKGROUND: #000; HEIGHT: 100%; OVERFLOW: hidden; TOP: 0px; LEFT: 0px
}
#supersized A.prevslide {
	Z-INDEX: -20
}
#supersized A.activeslide {
	Z-INDEX: -10
}
#supersized A.prevslide IMG {
	DISPLAY: inline
}
#supersized A.activeslide IMG {
	DISPLAY: inline
}
#controls-wrapper {
	Z-INDEX: 4; POSITION: fixed; MARGIN: 0px auto; WIDTH: 100%; BOTTOM: 0px; BACKGROUND: url(../img/nav-bg.png) repeat-x; HEIGHT: 62px; LEFT: 0px
}
#controls {
	Z-INDEX: 5; TEXT-ALIGN: left; PADDING-BOTTOM: 0px; PADDING-LEFT: 114px; PADDING-RIGHT: 114px; HEIGHT: 100%; OVERFLOW: hidden; PADDING-TOP: 0px
}
#slidecounter {
	MARGIN: 19px 10px 18px 20px; FONT: bold 23px "Helvetica Neue", Helvetica, Arial, sans-serif; FLOAT: left; COLOR: #888; text-shadow: #000 0 -1px 0
}
#slidecaption {
	MARGIN: 23px 20px 23px 0px; FONT: bold 16px "Helvetica Neue", Helvetica, Arial, sans-serif; FLOAT: left; COLOR: #fff; OVERFLOW: hidden; text-shadow: #000 0 2px 0
}
#navigation {
	MARGIN: 10px 20px 0px 0px; FLOAT: right
}
#nextthumb {
	Z-INDEX: 6; BORDER-BOTTOM: #fff 2px solid; POSITION: fixed; BORDER-LEFT: #fff 2px solid; WIDTH: 100px; BOTTOM: 12px; DISPLAY: none; BACKGROUND: #ddd; HEIGHT: 75px; OVERFLOW: hidden; BORDER-TOP: #fff 2px solid; BORDER-RIGHT: #fff 2px solid; -webkit-box-shadow: 0 0 5px #000
}
#prevthumb {
	Z-INDEX: 6; BORDER-BOTTOM: #fff 2px solid; POSITION: fixed; BORDER-LEFT: #fff 2px solid; WIDTH: 100px; BOTTOM: 12px; DISPLAY: none; BACKGROUND: #ddd; HEIGHT: 75px; OVERFLOW: hidden; BORDER-TOP: #fff 2px solid; BORDER-RIGHT: #fff 2px solid; -webkit-box-shadow: 0 0 5px #000
}
#nextthumb {
	RIGHT: 12px
}
#prevthumb {
	LEFT: 12px
}
#nextthumb IMG {
	WIDTH: 150px; HEIGHT: auto
}
#prevthumb IMG {
	WIDTH: 150px; HEIGHT: auto
}
#nextthumb:active {
	BOTTOM: 10px
}
#prevthumb:active {
	BOTTOM: 10px
}
#navigation > :hover {
	CURSOR: pointer
}
#nextthumb:hover {
	CURSOR: pointer
}
#prevthumb:hover {
	CURSOR: pointer
}


/* slider, supersized */
#gotoeshop {
	display:inline;
	float:right;
	padding: 10px 20px;
	text-decoration:none;
	font-weight:bold;
	border-radius: 5px ;
	-moz-border-radius: 5px

}
#gotoeshop:link, #gotoeshop:visited  {
	color:#000;
	background:#f6b51b ;
}
 #gotoeshop:hover,  #gotoeshop:active,  #gotoeshop:focus{
	color:#fff;
	background:#000
}

#gotoback {
  display: inline;
  float:left;
  margin: 5px 0;
  padding: 5px 10px;
  text-decoration:none;
  font-weight:bold;
  border-radius: 5px ;
  -moz-border-radius: 5px
}
a#gotoback:link, a#gotoback:visited  {
  color:#fff;
  background:#000 ;
}
 a#gotoback:hover,  a#gotoback:active,  a#gotoback:focus{
  color:#000;
 background:#f6b51b ;
}
#orderby {
	width: 100%;
	overflow:hidden;
	padding: 0 0 10px 0;
	text-align:right
}
#orderby div {
	display:inline;
	float:right;
	margin: 0 5px 0 0;
}
#orderby a {
  padding: 5px 10px;
  margin: 0 10px 0 0;
  display:inline;
  float: left;
  text-decoration:none;
  color:#000;
  background:#f6b51b ;
  font-weight:bold;
  border-radius: 5px ;
  -moz-border-radius: 5px
}
#orderby span {
 padding: 5px 10px;
  display:inline;
  float: left;
}
#orderby a:hover,  #orderby a:active, #orderby a:focus{
  color:#fff;
  background:#000
}

#orderby a.selected {
  color:#fff;
  background:#000
}
p.price input {
	margin-left: 5px;
	margin-right: 5px;
}
p.price input.button {
  padding: 5px 0;
  text-decoration:none;
  color:#000;
  background:#f6b51b ;
  font-weight:bold;
  border-radius: 5px ;
  -moz-border-radius: 5px ;
  width: 60px;
  margin-right: 0;
  margin-left: 0
}
p.price input.button:hover {
	background-color:#000;
	color:#fff;
	border-color:#fff !important
}
div.submit a {
	padding: 7px 10px;
	 background:#f6b51b ;
	  font-weight:bold;
  border-radius: 5px ;
  -moz-border-radius: 5px ;
  text-decoration:none
}
div.submit a:link, div.submit a:visited {
	color:#000;
}
div.submit a:hover, div.submit a:active, div.submit a:focus {
	background: #000;
	color:#fff
}
@media (max-width: 640px) {
	div.submit a, div.submit input {
		display: inline-block;
		margin: 5px;
	}
}
.cls { clear: both; }
.clearfix:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
.clearfix {
	display: inline-block;
}
html[xmlns] .clearfix {
	display: block;
}
* html .clearfix {
	height: 1%;
}
.facebook {
	display: none;
}
@media (min-width: 641px) {
	.facebook {
		text-align: right;
		position: absolute;
		right: 20px;
		top: 0;
		display: block;
	}
}
/* modal okno */
.modal {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: none;
  overflow: hidden;
  width: 100%;
  background-color: rgba( 0, 0, 0, 0.6 );
}
.modal.is-open { display: block; }
.modal h2 {
	font-weight: bold;
	margin-bottom: 15px;
}
.modal__body {
  position: absolute;
  max-width: 650px;
  padding: 15px;
  background: #fff;
  box-shadow: 0 20px 50px rgba( 0, 0, 0, 0.6 );
}
@media (min-width: 500px) {
	.modal__body {
  	top: 50px;
  	padding: 25px 35px;
  	width: 100%;
	  left: 50%;
	  transform: translate( -50%, 0 );
	}

	.modal p {
		font-size: 18px;
		padding-bottom: 15px;
	}
}
.modal__close {
  position: absolute;
  top: 10px;
  right: 10px;
  overflow: hidden;
  width: 30px;
  height: 30px;
  font-size: 20px;
  border-radius: 20px;
  background-color: #10710b;
  cursor: pointer;
  color: #fff;
  font-weight: bold;
}
.modal__close a {
  color: #fff;
  text-decoration: none;
}