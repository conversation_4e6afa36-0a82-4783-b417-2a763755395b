body {
  font: 13px "Trebuchet MS", "Geneva CE", lucida, sans-serif;
  color: #000;
  background: #808080; 
  padding: 50px 0
}

h1 {
  font-size: 150%;
  
}

h2 {
  font-size: 120%;
}

img {
  border: 0px;
}

form label {
  text-align: left;
}

#content {
  width: 90%;
  margin: 0 auto;
  background: #FFFFFF;
  padding: 2%
}
#home #content {
		
}
#footer {
	background: silver;
	color:#000;
	margin: 20px 0;
	padding: 10px 20px;
	zoom:1
}
form {
	background: #FFFFFF;
	padding: 5px;
	border: 1px solid black
}
#homef form {
	border: 1px solid black
	
}
h3 {
	font-size: 130%;
	margin: 20px 0 0 0;
	padding: 10px 0;
}
#home h3 {
	padding: 10px 20px;	
}
legend {
	font-size: 110%;
	padding-bottom: 0px;
	
}
table {
	background:#fff;
	color:#000;
  margin-top: 5px
}
#home form table {
	border: 5px solid #f0ed00		
}
th, td {
	text-align:left;	
	padding: 5px 10px;
}
#home table td, #home table th {
	padding: 10px;	
}
p {
	margin: 0;
	padding: 10px 20px;	
	background:#fff;
	color:#000
}

form input.default {
  
}

fieldset {
  border:none;
  padding: 5px;  
}

#formbutton {
  margin-left: 100px;
}
input.button {
	padding: 5px 10px;
	cursor:pointer;
	cursor:hand	
}
a:link, a:visited {
  color: #000;
  font-weight:bold;
}
a:hover {
	color:#840000	
}
#message {
  margin: 10px 0;
  color: #f00;
}

#logged-in {
  margin-top: 3em;
}

.flash {
  color: black;
  background: #CAFFCA;
  padding: 1em;
  margin: 1em auto;
  border: 5px solid #a4ffa3;
  font-weight: bolder
}

.err {
  color: black;
  background: #FFC0C0;
  border: 1px solid darkred;
  padding: 1em;
  margin: 1em 0;
  font-weight: bolder
}

.error {
  color: darkred;
  padding: 1em;
  margin-left: 10px;
  font-weight: bolder
}

.required {
  color: darkred;
}

.warning {
  color: black;
  background: #FFC0C0;
  padding: 1em;
  margin: 1em auto;
  border: 5px solid #ff999c
}

table.grid {
  padding: 0;
  margin: 10;
  border-collapse:collapse;
}

table.grid td, table.grid th {
  border: 1px solid black;
  padding: 2px 2px 2px 4px;
}

table.grid th {
  background: silver ;
  text-align: left;
}

table.grid .alt td {
  background: #f8f8f0;
}


#menu-top, #submenu {
	zoom: 1;
	overflow:hidden;
	
		
}
#menu-top p, #submenu p {
	padding: 0;
	margin: 0;
	padding: 10px 0;
	overflow:hidden;
	width:100%	
}
#menu-top span, #submenu span {
 display: inline;
 float:left;
 background: #808080;
 margin: 0 5px 0 0 ;
}
#menu-top a, #submenu a {
	padding: 5px 10px;
	display: inline;
	float:left;
	zoom: 1
}
#menu-top a:link, #menu-top a:visited {
	color: #fff	
}
#menu-top a:hover {
	background: #840000	
}
#submenu a {
	background: #ACACAC	
} 
#submenu a:link, #submenu a:visited {
	color:#fff;	
}
#submenu a:hover {
	background: #840000;
	color:#fefefe	
}

#menu-login {
  padding: 0px; 
  font-size: 85%;
  text-align: right;
}

#menu-top span.current,  #submenu span.current {
  font-weight: bolder;
  color: black;
  background: #FFFFFF;
}

.current {
  font-weight: bolder;
  color: black;
}

#ajax-spinner {
  margin: 15px 0 0 15px;
  padding: 13px;
  background: white url('../ico/spinner.gif') no-repeat 50% 50%;
  font-size: 0;
  z-index: 123456;
  display: none;
}