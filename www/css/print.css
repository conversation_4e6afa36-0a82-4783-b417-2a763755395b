body {
	width: 100% !important;
	margin: 0 !important;
	padding: 0 !important;
	line-height: 1.4;
	word-spacing: 1.1pt;
	letter-spacing: 0.2pt;
	font-family:  sans-serif;
	color: #000;
	background: none;
	font-size: 10pt
}
.end {
	clear:both	
}
h1, h2, h3, h4, h5, h6 {
	font-family:  sans-serif
}
h1 {
	font-size: 15pt
}
h2 {
	font-size: 13pt;
}
h3 {
	font-size: 11pt
}
h4, h5, h6 {
	font-size: 10pt
}
code {
	font: 10pt Courier, monospace
}
blockquote {
	margin: 1.3em;
	padding: 1em;
	font-size: 10pt
}
hr {
	background-color: #ccc
}
img {
	margin: 1em 1.5em 1.5em 1.5em;
	float: right;
	border: 3px solid #c0c0c0
}
a:link, a:visited {
	background: transparent;
	font-weight: 700;
	text-decoration: underline;
	color: #333
}
address {
	font-style: normal;
	padding: 0 0 1.5em 0
}
#search, #menu, #nav, #foot, #detail form, p.addbasket, #supersized img, hr, p.price, #orderby, #next img, #slider, .paginator, .basket {
	display:none !important;	
}
.table {
	width: 100%;
	padding: 0 0 1.5em;
}
table {
	border-collapse: collapse;
	font-size: 100%;
	width: 100%;
}
th, td {
	padding: 0.2em 1em;
	text-align: left;
	border: 1px solid #d0d0d0
}
#kat, #next {
	width: 100%;
	overflow:hidden;
	padding-bottom: 1cm	
}
.kat {
	border: 1px solid #cecece;
	float: left;
	display: inline;
	width: 8cm;
	padding: 5px;
	margin: 0 5px 5px 0
}

#detail img {
	border: none;
	margin: 0 0.2cm 0.2cm 0;
	float: right
}

#next p {
	width: 33%;
	padding: 0;
	margin: 0;
	float:left;
}
#next p a {
	text-decoration:none
		
}