function DeleteConfirmFront(msgadd) {
  return window.confirm(msgadd);
}

function DeleteConfirm(msgadd) {
  return window.confirm("Opravdu chcete smazat " + msgadd + " ?");
}

function zobrazSkryj(idecko){
  el=document.getElementById(idecko).style;
  el.display=(el.display == 'block')?'none':'block';
  return false;
}

$(document).ready(function() {
  // ovládání modal okna
  if ($('.modal').length) {
    // zavření modal okna
    $('.modal__close').on('click', function(event) {
      event.preventDefault();
      $(this)
        .closest('.modal')
        .removeClass('is-open');
    });

    // zavření okna kliknutím na pozadí
    $('.modal').on('click', function(event) {
      event.preventDefault();
      $(this).removeClass('is-open');
    });

    // zamezení zavření po kliknutí na tělo modalu
    $('.modal__body').on('click', function(event) {
      event.stopPropagation();
    });
  }
});
