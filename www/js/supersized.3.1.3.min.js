/*
	Supersized - Fullscreen Slideshow jQuery Plugin
	Version 3.1.3
	www.buildinternet.com/project/supersized
	
	By <PERSON> / One Mighty Roar (www.onemightyroar.com)
	Released under MIT License / GPL License
*/

(function(a){a(document).ready(function(){a("body").prepend('<div id="supersized-loader"></div>').prepend('<div id="supersized"></div>')});a.supersized=function(t){var c={slideshow:1,autoplay:1,start_slide:1,random:0,slide_interval:5000,transition:1,transition_speed:750,new_window:1,pause_hover:0,keyboard_nav:1,performance:1,image_protect:1,image_path:"img/",min_width:0,min_height:0,vertical_center:1,horizontal_center:1,fit_portrait:0,fit_landscape:0,navigation:1,thumbnail_navigation:0,slide_counter:1,slide_captions:1};var k=a("#supersized");var d="#pauseplay";if(t){var t=a.extend(c,t)}else{var t=a.extend(c)}var b=false;var n=false;var m=t.image_path;if(t.start_slide){var l=t.start_slide-1}else{var l=Math.floor(Math.random()*t.slides.length)}var s=t.new_window?' target="_blank"':"";if(t.performance==3){k.addClass("speed")}else{if((t.performance==1)||(t.performance==2)){k.addClass("quality")}}if(t.random){arr=t.slides;for(var f,q,h=arr.length;h;f=parseInt(Math.random()*h),q=arr[--h],arr[h]=arr[f],arr[f]=q){}t.slides=arr}if(t.slides.length>1){l-1<0?loadPrev=t.slides.length-1:loadPrev=l-1;var e=(t.slides[loadPrev].url)?"href='"+t.slides[loadPrev].url+"'":"";a("<img/>").attr("src",t.slides[loadPrev].image).appendTo(k).wrap("<a "+e+s+"></a>")}e=(t.slides[l].url)?"href='"+t.slides[l].url+"'":"";a("<img/>").attr("src",t.slides[l].image).appendTo(k).wrap('<a class="activeslide" '+e+s+"></a>");if(t.slides.length>1){l==t.slides.length-1?loadNext=0:loadNext=l+1;e=(t.slides[loadNext].url)?"href='"+t.slides[loadNext].url+"'":"";a("<img/>").attr("src",t.slides[loadNext].image).appendTo(k).wrap("<a "+e+s+"></a>")}k.hide();a("#controls-wrapper").hide();a(document).ready(function(){r()});a(window).load(function(){a("#supersized-loader").hide();k.fadeIn("fast");a("#controls-wrapper").show();if(t.thumbnail_navigation){l-1<0?prevThumb=t.slides.length-1:prevThumb=l-1;a("#prevthumb").show().html(a("<img/>").attr("src",t.slides[prevThumb].image));l==t.slides.length-1?nextThumb=0:nextThumb=l+1;a("#nextthumb").show().html(a("<img/>").attr("src",t.slides[nextThumb].image))}r();if(t.slide_captions){a("#slidecaption").html(t.slides[l].title)}if(!(t.navigation)){a("#navigation").hide()}if(t.slideshow&&t.slides.length>1){if(t.slide_counter){a("#slidecounter .slidenumber").html(l+1);a("#slidecounter .totalslides").html(t.slides.length)}slideshow_interval=setInterval(o,t.slide_interval);if(!(t.autoplay)){clearInterval(slideshow_interval);n=true;if(a(d).attr("src")){a(d).attr("src",m+"play_dull.png")}}if(t.thumbnail_navigation){a("#nextthumb").click(function(){if(b){return false}clearInterval(slideshow_interval);o(k,t);if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false});a("#prevthumb").click(function(){if(b){return false}clearInterval(slideshow_interval);g(k,t);if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false})}if(t.navigation){a("#navigation a").click(function(){a(this).blur();return false});a("#nextslide").click(function(){if(b){return false}clearInterval(slideshow_interval);o();if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false});if(a("#nextslide").attr("src")){a("#nextslide").mousedown(function(){a(this).attr("src",m+"forward.png")});a("#nextslide").mouseup(function(){a(this).attr("src",m+"forward_dull.png")});a("#nextslide").mouseout(function(){a(this).attr("src",m+"forward_dull.png")})}a("#prevslide").click(function(){if(b){return false}clearInterval(slideshow_interval);g();if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false});if(a("#prevslide").attr("src")){a("#prevslide").mousedown(function(){a(this).attr("src",m+"back.png")});a("#prevslide").mouseup(function(){a(this).attr("src",m+"back_dull.png")});a("#prevslide").mouseout(function(){a(this).attr("src",m+"back_dull.png")})}a(d).click(function(){if(b){return false}if(n){if(a(d).attr("src")){a(d).attr("src",m+"pause_dull.png")}n=false;slideshow_interval=setInterval(o,t.slide_interval)}else{if(a(d).attr("src")){a(d).attr("src",m+"play_dull.png")}clearInterval(slideshow_interval);n=true}return false})}}});if(t.keyboard_nav){a(document.documentElement).keydown(function(i){if((i.keyCode==37)||(i.keyCode==40)){if(a("#prevslide").attr("src")){a("#prevslide").attr("src",m+"back.png")}}else{if((i.keyCode==39)||(i.keyCode==38)){if(a("#nextslide").attr("src")){a("#nextslide").attr("src",m+"forward.png")}}}});a(document.documentElement).keyup(function(i){clearInterval(slideshow_interval);if((i.keyCode==37)||(i.keyCode==40)){if(a("#prevslide").attr("src")){a("#prevslide").attr("src",m+"back_dull.png")}if(b){return false}clearInterval(slideshow_interval);g();if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false}else{if((i.keyCode==39)||(i.keyCode==38)){if(a("#nextslide").attr("src")){a("#nextslide").attr("src",m+"forward_dull.png")}if(b){return false}clearInterval(slideshow_interval);o();if(!(n)){slideshow_interval=setInterval(o,t.slide_interval)}return false}else{if(i.keyCode==32){if(b){return false}if(n){if(a(d).attr("src")){a(d).attr("src",m+"pause_dull.png")}n=false;slideshow_interval=setInterval(o,t.slide_interval)}else{if(a(d).attr("src")){a(d).attr("src",m+"play_dull.png")}n=true}return false}}}})}if(t.slideshow&&t.pause_hover){a(k).hover(function(){if(b){return false}if(!(n)&&t.navigation){if(a(d).attr("src")){a(d).attr("src",m+"pause.png")}clearInterval(slideshow_interval)}},function(){if(!(n)&&t.navigation){if(a(d).attr("src")){a(d).attr("src",m+"pause_dull.png")}slideshow_interval=setInterval(o,t.slide_interval)}})}a(window).resize(function(){r()});function r(){return k.each(function(){var i=a("img",k);a(i).each(function(){var u=(a(this).height()/a(this).width()).toFixed(2);thisSlide=a(this);var j=a(window).width();var w=a(window).height();var x;if((w<=t.min_height)&&(j<=t.min_width)){if((w/j)>u){t.fit_landscape&&u<=1?v(true):y(true)}else{t.fit_portrait&&u>1?y(true):v(true)}}else{if(j<=t.min_width){if((w/j)>u){t.fit_landscape&&u<=1?v(true):y()}else{t.fit_portrait&&u>1?y():v(true)}}else{if(w<=t.min_height){if((w/j)>u){t.fit_landscape&&u<=1?v():y(true)}else{t.fit_portrait&&u>1?y(true):v()}}else{if((w/j)>u){t.fit_landscape&&u<=1?v():y()}else{t.fit_portrait&&u>1?y():v()}}}}function v(z){if(z){if(thisSlide.width()<j||thisSlide.width()<t.min_width){if(thisSlide.width()*u>=t.min_height){thisSlide.width(t.min_width);thisSlide.height(thisSlide.width()*u)}else{y()}}}else{if(t.min_height>=w&&!t.fit_landscape){if(j*u>=t.min_height||(j*u>=t.min_height&&u<=1)){thisSlide.width(j);thisSlide.height(j*u)}else{if(u>1){thisSlide.height(t.min_height);thisSlide.width(thisSlide.height()/u)}else{if(thisSlide.width()<j){thisSlide.width(j);thisSlide.height(thisSlide.width()*u)}}}}else{thisSlide.width(j);thisSlide.height(j*u)}}}function y(z){if(z){if(thisSlide.height()<w){if(thisSlide.height()/u>=t.min_width){thisSlide.height(t.min_height);thisSlide.width(thisSlide.height()/u)}else{v(true)}}}else{if(t.min_width>=j){if(w/u>=t.min_width||u>1){thisSlide.height(w);thisSlide.width(w/u)}else{if(u<=1){thisSlide.width(t.min_width);thisSlide.height(thisSlide.width()*u)}}}else{thisSlide.height(w);thisSlide.width(w/u)}}}if(t.horizontal_center){a(this).css("left",(j-a(this).width())/2)}if(t.vertical_center){a(this).css("top",(w-a(this).height())/2)}});if(t.image_protect){a("img",k).bind("contextmenu",function(){return false});a("img",k).bind("mousedown",function(){return false})}return false})}function o(){if(b){return false}else{b=true}var u=t.slides;var j=k.find(".activeslide");j.removeClass("activeslide");if(j.length==0){j=k.find("a:last")}var i=j.next().length?j.next():k.find("a:first");var v=i.prev().length?i.prev():k.find("a:last");a(".prevslide").removeClass("prevslide");v.addClass("prevslide");l+1==u.length?l=0:l++;if(t.performance==1){k.removeClass("quality").addClass("speed")}loadSlide=false;l==u.length-1?loadSlide=0:loadSlide=l+1;e=(t.slides[loadSlide].url)?"href='"+t.slides[loadSlide].url+"'":"";a("<img/>").attr("src",t.slides[loadSlide].image).appendTo(k).wrap("<a "+e+s+"></a>");if(t.thumbnail_navigation==1){l-1<0?prevThumb=u.length-1:prevThumb=l-1;a("#prevthumb").html(a("<img/>").attr("src",t.slides[prevThumb].image));nextThumb=loadSlide;a("#nextthumb").html(a("<img/>").attr("src",t.slides[nextThumb].image))}j.prev().remove();if(t.slide_counter){a("#slidecounter .slidenumber").html(l+1)}if(t.slide_captions){(t.slides[l].title)?a("#slidecaption").html(t.slides[l].title):a("#slidecaption").html("")}i.hide().addClass("activeslide");switch(t.transition){case 0:i.show();b=false;break;case 1:i.fadeTo(t.transition_speed,1,function(){p()});break;case 2:i.animate({top:-a(window).height()},0).show().animate({top:0},t.transition_speed,function(){p()});break;case 3:i.animate({left:a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});break;case 4:i.animate({top:a(window).height()},0).show().animate({top:0},t.transition_speed,function(){p()});break;case 5:i.animate({left:-a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});break;case 6:i.animate({left:a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});j.animate({left:-a(window).width()},t.transition_speed);break;case 7:i.animate({left:-a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});j.animate({left:a(window).width()},t.transition_speed);break}}function g(){if(b){return false}else{b=true}var u=t.slides;var j=k.find(".activeslide");j.removeClass("activeslide");if(j.length==0){j=a(k).find("a:first")}var i=j.prev().length?j.prev():a(k).find("a:last");var v=i.next().length?i.next():a(k).find("a:first");a(".prevslide").removeClass("prevslide");v.addClass("prevslide");l==0?l=u.length-1:l--;if(t.performance==1){k.removeClass("quality").addClass("speed")}loadSlide=false;l-1<0?loadSlide=u.length-1:loadSlide=l-1;e=(t.slides[loadSlide].url)?"href='"+t.slides[loadSlide].url+"'":"";a("<img/>").attr("src",t.slides[loadSlide].image).prependTo(k).wrap("<a "+e+s+"></a>");if(t.thumbnail_navigation==1){prevThumb=loadSlide;a("#prevthumb").html(a("<img/>").attr("src",t.slides[prevThumb].image));l==u.length-1?nextThumb=0:nextThumb=l+1;a("#nextthumb").html(a("<img/>").attr("src",t.slides[nextThumb].image))}j.next().remove();if(t.slide_counter){a("#slidecounter .slidenumber").html(l+1)}if(t.slide_captions){(t.slides[l].title)?a("#slidecaption").html(t.slides[l].title):a("#slidecaption").html("")}i.hide().addClass("activeslide");switch(t.transition){case 0:i.show();b=false;break;case 1:i.fadeTo(t.transition_speed,1,function(){p()});break;case 2:i.animate({top:a(window).height()},0).show().animate({top:0},t.transition_speed,function(){p()});break;case 3:i.animate({left:-a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});break;case 4:i.animate({top:-a(window).height()},0).show().animate({top:0},t.transition_speed,function(){p()});break;case 5:i.animate({left:a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});break;case 6:i.animate({left:-a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});j.animate({left:a(window).width()},t.transition_speed);break;case 7:i.animate({left:a(window).width()},0).show().animate({left:0},t.transition_speed,function(){p()});j.animate({left:-a(window).width()},t.transition_speed);break}}function p(){b=false;if(t.performance==1){k.removeClass("speed").addClass("quality")}r()}}})(jQuery);