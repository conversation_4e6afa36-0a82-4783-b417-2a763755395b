  $( function() {
      $("#catname_search").autocomplete({
          source: basePath + "/administrace/product/search-ac",
          minLength: 3,
          select: function (event, ui) {
              $("#catid_search").val(ui.item.id);
          }
      });

      $('#catname_search').keyup(function() {
        if( !$(this).val() ) {
          $("#catid_search").val('');
        }
      });
  });

$   ( function() {
      $("#catname_edit").autocomplete({
          source: basePath + "/administrace/catalog/search-ac",
          minLength: 3,
          select: function (event, ui) {
              $("#catid_edit").val(ui.item.id);
          }
      });

      $('#catname_edit').keyup(function() {
        if( !$(this).val() ) {
          $("#catid_edit").val('');
        }
      });
  });

  function clearAc(sufix) {
      $("#catid_"+sufix).val('');
      $("#catname_"+sufix).val('');
      return false;
  }